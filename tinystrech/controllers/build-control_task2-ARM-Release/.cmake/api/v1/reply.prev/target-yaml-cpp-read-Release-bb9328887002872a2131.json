{"artifacts": [{"path": "/home/<USER>/Documents/tinystrech/controllers/build/read"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "INCLUDE_DIRECTORIES", "include_directories", "set_target_properties"], "files": ["/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/util/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 3, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 28, "parent": 2}, {"command": 2, "file": 0, "line": 7, "parent": 0}, {"command": 3, "file": 1, "line": 7, "parent": 2}, {"command": 4, "file": 1, "line": 12, "parent": 2}, {"command": 4, "file": 1, "line": 29, "parent": 2}, {"command": 4, "file": 1, "line": 31, "parent": 2}, {"command": 4, "file": 1, "line": 32, "parent": 2}, {"command": 5, "file": 0, "line": 29, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=c++11 -O3 -pthread -O3 -DNDEBUG"}, {"fragment": "-std=gnu++11"}], "defines": [{"define": "__USE_SINGLE_PRECISION__"}], "includes": [{"backtrace": 5, "path": "/usr/include/eigen3"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_vmc"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_wb"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vmc_inc"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vision_location"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/cube_vision"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/include"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils"}, {"backtrace": 7, "path": "/usr/local/include"}, {"backtrace": 8, "path": "/home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include"}, {"backtrace": 9, "path": "/home/<USER>/Downloads/boost_cross/include"}, {"backtrace": 4, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/include"}], "language": "CXX", "languageStandard": {"backtraces": [10], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 4, "id": "yaml-cpp::@306ed2d68c6501e8728f"}], "id": "yaml-cpp-read::@cc8e4066b47865546cb0", "link": {"commandFragments": [{"fragment": "-std=c++11 -O3 -pthread -O3 -DNDEBUG", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "-L/usr/local/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/usr/local/lib", "role": "libraries"}, {"backtrace": 4, "fragment": "../libyaml-cpp.a", "role": "libraries"}], "language": "CXX"}, "name": "yaml-cpp-read", "nameOnDisk": "read", "paths": {"build": "lib/util", "source": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/util"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/util/read.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}