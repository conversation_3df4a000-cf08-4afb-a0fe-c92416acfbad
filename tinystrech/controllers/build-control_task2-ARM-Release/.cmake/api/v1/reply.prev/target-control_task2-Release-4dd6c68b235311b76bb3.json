{"artifacts": [{"path": "/home/<USER>/Documents/tinystrech/controllers/build/control_task2"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_EXECUTABLE", "link_directories", "target_link_libraries", "add_definitions", "add_compile_definitions", "INCLUDE_DIRECTORIES", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 45, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 2, "file": 0, "line": 50, "parent": 0}, {"command": 2, "file": 0, "line": 53, "parent": 0}, {"command": 3, "file": 0, "line": 65, "parent": 0}, {"command": 4, "file": 0, "line": 5, "parent": 0}, {"command": 5, "file": 0, "line": 7, "parent": 0}, {"command": 6, "file": 0, "line": 12, "parent": 0}, {"command": 6, "file": 0, "line": 29, "parent": 0}, {"command": 6, "file": 0, "line": 31, "parent": 0}, {"command": 6, "file": 0, "line": 32, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=c++11 -O3 -pthread -O3 -DNDEBUG"}], "defines": [{"backtrace": 5, "define": "RUN_WEBOTS=0"}, {"backtrace": 6, "define": "__USE_SINGLE_PRECISION__"}], "includes": [{"backtrace": 7, "path": "/usr/include/eigen3"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_vmc"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_wb"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vmc_inc"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vision_location"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/cube_vision"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/include"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils"}, {"backtrace": 9, "path": "/usr/local/include"}, {"backtrace": 10, "path": "/home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include"}, {"backtrace": 11, "path": "/home/<USER>/Downloads/boost_cross/include"}, {"backtrace": 4, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]}, {"compileCommandFragments": [{"fragment": "-O3 -DNDEBUG"}], "defines": [{"backtrace": 5, "define": "RUN_WEBOTS=0"}, {"backtrace": 6, "define": "__USE_SINGLE_PRECISION__"}], "includes": [{"backtrace": 7, "path": "/usr/include/eigen3"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_vmc"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_wb"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vmc_inc"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vision_location"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/cube_vision"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/include"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model"}, {"backtrace": 8, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils"}, {"backtrace": 9, "path": "/usr/local/include"}, {"backtrace": 10, "path": "/home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include"}, {"backtrace": 11, "path": "/home/<USER>/Downloads/boost_cross/include"}, {"backtrace": 4, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/include"}], "language": "C", "sourceIndexes": [32]}], "dependencies": [{"backtrace": 3, "id": "qpOASES::@306ed2d68c6501e8728f"}, {"backtrace": 4, "id": "yaml-cpp::@306ed2d68c6501e8728f"}], "id": "control_task2::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-std=c++11 -O3 -pthread -O3 -DNDEBUG", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 2, "fragment": "-L/usr/local/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/usr/local/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "lib/libs/libqpOASES.a", "role": "libraries"}, {"backtrace": 4, "fragment": "lib/libyaml-cpp.a", "role": "libraries"}], "language": "CXX"}, "name": "control_task2", "nameOnDisk": "control_task2", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/butler.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}