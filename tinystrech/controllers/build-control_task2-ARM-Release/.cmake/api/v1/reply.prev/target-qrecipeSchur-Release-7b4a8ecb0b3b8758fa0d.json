{"artifacts": [{"path": "lib/bin/qrecipeSchur"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_EXECUTABLE", "link_directories", "TARGET_LINK_LIBRARIES", "INCLUDE_DIRECTORIES", "include_directories"], "files": ["/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 149, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 28, "parent": 2}, {"command": 2, "file": 0, "line": 150, "parent": 0}, {"command": 3, "file": 1, "line": 7, "parent": 2}, {"command": 4, "file": 1, "line": 12, "parent": 2}, {"command": 4, "file": 1, "line": 29, "parent": 2}, {"command": 4, "file": 1, "line": 31, "parent": 2}, {"command": 4, "file": 1, "line": 32, "parent": 2}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=c++11 -O3 -pthread -D__NO_COPYRIGHT__ -Wall -pedantic -Wfloat-equal -Wshadow -DLINUX -g -O3 -finline-functions"}], "defines": [{"define": "__USE_SINGLE_PRECISION__"}], "includes": [{"backtrace": 5, "path": "/usr/include/eigen3"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_vmc"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_wb"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vmc_inc"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vision_location"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/cube_vision"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/include"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model"}, {"backtrace": 6, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils"}, {"backtrace": 7, "path": "/usr/local/include"}, {"backtrace": 8, "path": "/home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include"}, {"backtrace": 9, "path": "/home/<USER>/Downloads/boost_cross/include"}], "language": "CXX", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 4, "id": "qpOASES::@306ed2d68c6501e8728f"}], "id": "qrecipeSchur::@306ed2d68c6501e8728f", "link": {"commandFragments": [{"fragment": "-std=c++11 -O3 -pthread -D__NO_COPYRIGHT__ -Wall -pedantic -Wfloat-equal -Wshadow -DLINUX -g -O3 -finline-functions", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"backtrace": 3, "fragment": "-L/usr/local/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/usr/local/lib", "role": "libraries"}, {"backtrace": 4, "fragment": "libs/libqpOASES.a", "role": "libraries"}], "language": "CXX"}, "name": "qrecipeSchur", "nameOnDisk": "qrecipeSchur", "paths": {"build": "lib", "source": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/examples/qrecipeSchur.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}