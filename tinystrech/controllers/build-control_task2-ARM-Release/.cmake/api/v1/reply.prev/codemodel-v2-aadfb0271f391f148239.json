{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "hasInstallRule": true, "minimumCMakeVersion": {"string": "2.6"}, "projectIndex": 0, "source": ".", "targetIndexes": [28]}, {"build": "lib", "hasInstallRule": true, "minimumCMakeVersion": {"string": "2.6"}, "parentIndex": 0, "projectIndex": 1, "source": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES", "targetIndexes": [29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40]}, {"build": "lib", "childIndexes": [3], "minimumCMakeVersion": {"string": "3.4"}, "parentIndex": 0, "projectIndex": 2, "source": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 41]}, {"build": "lib/util", "minimumCMakeVersion": {"string": "3.4"}, "parentIndex": 2, "projectIndex": 2, "source": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/util", "targetIndexes": [42, 43, 44]}], "name": "Release", "projects": [{"childIndexes": [1, 2], "directoryIndexes": [0], "name": "control_task2", "targetIndexes": [28]}, {"directoryIndexes": [1], "name": "qpOASES", "parentIndex": 0, "targetIndexes": [29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40]}, {"directoryIndexes": [2, 3], "name": "YAML_CPP", "parentIndex": 0, "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 41, 42, 43, 44]}], "targets": [{"directoryIndex": 2, "id": "Continuous::@306ed2d68c6501e8728f", "jsonFile": "target-Continuous-Release-32bfef7f8d26716a6122.json", "name": "Continuous", "projectIndex": 2}, {"directoryIndex": 2, "id": "ContinuousBuild::@306ed2d68c6501e8728f", "jsonFile": "target-ContinuousBuild-Release-444db2b156b75685daf0.json", "name": "ContinuousBuild", "projectIndex": 2}, {"directoryIndex": 2, "id": "ContinuousConfigure::@306ed2d68c6501e8728f", "jsonFile": "target-ContinuousConfigure-Release-648d85cd5e6712843950.json", "name": "ContinuousConfigure", "projectIndex": 2}, {"directoryIndex": 2, "id": "ContinuousCoverage::@306ed2d68c6501e8728f", "jsonFile": "target-ContinuousCoverage-Release-f066d1ceb1614123f7ca.json", "name": "ContinuousCoverage", "projectIndex": 2}, {"directoryIndex": 2, "id": "ContinuousMemCheck::@306ed2d68c6501e8728f", "jsonFile": "target-ContinuousMemCheck-Release-902a8ecfb6c53c8eda5c.json", "name": "Continuous<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectIndex": 2}, {"directoryIndex": 2, "id": "ContinuousStart::@306ed2d68c6501e8728f", "jsonFile": "target-ContinuousStart-Release-10886cbb346d56e4e44b.json", "name": "ContinuousStart", "projectIndex": 2}, {"directoryIndex": 2, "id": "ContinuousSubmit::@306ed2d68c6501e8728f", "jsonFile": "target-ContinuousSubmit-Release-cb9e5c16c021c4f4006a.json", "name": "ContinuousSubmit", "projectIndex": 2}, {"directoryIndex": 2, "id": "ContinuousTest::@306ed2d68c6501e8728f", "jsonFile": "target-ContinuousTest-Release-2f53b454bcb168c0f4a1.json", "name": "ContinuousTest", "projectIndex": 2}, {"directoryIndex": 2, "id": "ContinuousUpdate::@306ed2d68c6501e8728f", "jsonFile": "target-ContinuousUpdate-Release-9c5a67d0870d84b0e142.json", "name": "ContinuousUpdate", "projectIndex": 2}, {"directoryIndex": 2, "id": "Experimental::@306ed2d68c6501e8728f", "jsonFile": "target-Experimental-Release-1af6087f4bc57590e339.json", "name": "Experimental", "projectIndex": 2}, {"directoryIndex": 2, "id": "ExperimentalBuild::@306ed2d68c6501e8728f", "jsonFile": "target-ExperimentalBuild-Release-d902bed22fc98114b692.json", "name": "ExperimentalBuild", "projectIndex": 2}, {"directoryIndex": 2, "id": "ExperimentalConfigure::@306ed2d68c6501e8728f", "jsonFile": "target-ExperimentalConfigure-Release-271b2a732241d9b8f523.json", "name": "ExperimentalConfigure", "projectIndex": 2}, {"directoryIndex": 2, "id": "ExperimentalCoverage::@306ed2d68c6501e8728f", "jsonFile": "target-ExperimentalCoverage-Release-b19cebe0e91ac5e9bd57.json", "name": "ExperimentalCoverage", "projectIndex": 2}, {"directoryIndex": 2, "id": "ExperimentalMemCheck::@306ed2d68c6501e8728f", "jsonFile": "target-ExperimentalMemCheck-Release-2e7cb38c11d01f35f9a5.json", "name": "ExperimentalMemCheck", "projectIndex": 2}, {"directoryIndex": 2, "id": "ExperimentalStart::@306ed2d68c6501e8728f", "jsonFile": "target-ExperimentalStart-Release-91f67acd83f6c3ca1c6f.json", "name": "ExperimentalStart", "projectIndex": 2}, {"directoryIndex": 2, "id": "ExperimentalSubmit::@306ed2d68c6501e8728f", "jsonFile": "target-ExperimentalSubmit-Release-a9e77a7157357c989594.json", "name": "ExperimentalSubmit", "projectIndex": 2}, {"directoryIndex": 2, "id": "ExperimentalTest::@306ed2d68c6501e8728f", "jsonFile": "target-ExperimentalTest-Release-66064249fb289813035f.json", "name": "ExperimentalTest", "projectIndex": 2}, {"directoryIndex": 2, "id": "ExperimentalUpdate::@306ed2d68c6501e8728f", "jsonFile": "target-ExperimentalUpdate-Release-f3ddaa5c5ce90ecf29db.json", "name": "ExperimentalUpdate", "projectIndex": 2}, {"directoryIndex": 2, "id": "Nightly::@306ed2d68c6501e8728f", "jsonFile": "target-Nightly-Release-203216344cf4ac76f3e8.json", "name": "Nightly", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlyBuild::@306ed2d68c6501e8728f", "jsonFile": "target-NightlyBuild-Release-ef261618643596c6eec8.json", "name": "NightlyBuild", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlyConfigure::@306ed2d68c6501e8728f", "jsonFile": "target-NightlyConfigure-Release-f64955475f6c7c7a8259.json", "name": "NightlyConfigure", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlyCoverage::@306ed2d68c6501e8728f", "jsonFile": "target-NightlyCoverage-Release-5b88b563677ef918fb20.json", "name": "NightlyCoverage", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlyMemCheck::@306ed2d68c6501e8728f", "jsonFile": "target-NightlyMemCheck-Release-15f9ecb403ee5e20ad34.json", "name": "NightlyMemCheck", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlyMemoryCheck::@306ed2d68c6501e8728f", "jsonFile": "target-NightlyMemoryCheck-Release-0e325644bbe824632aa6.json", "name": "Nightly<PERSON><PERSON>ory<PERSON><PERSON><PERSON>", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlyStart::@306ed2d68c6501e8728f", "jsonFile": "target-NightlyStart-Release-66e5b6b9eb8546348889.json", "name": "NightlyStart", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlySubmit::@306ed2d68c6501e8728f", "jsonFile": "target-NightlySubmit-Release-9801bd7357f7004502f3.json", "name": "NightlySubmit", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlyTest::@306ed2d68c6501e8728f", "jsonFile": "target-NightlyTest-Release-9888ce77627e64dae3d3.json", "name": "NightlyTest", "projectIndex": 2}, {"directoryIndex": 2, "id": "NightlyUpdate::@306ed2d68c6501e8728f", "jsonFile": "target-NightlyUpdate-Release-19920e42f1cbeccb550a.json", "name": "NightlyUpdate", "projectIndex": 2}, {"directoryIndex": 0, "id": "control_task2::@6890427a1f51a3e7e1df", "jsonFile": "target-control_task2-Release-4dd6c68b235311b76bb3.json", "name": "control_task2", "projectIndex": 0}, {"directoryIndex": 1, "id": "example1::@306ed2d68c6501e8728f", "jsonFile": "target-example1-Release-d9b327fd105eacd25d50.json", "name": "example1", "projectIndex": 1}, {"directoryIndex": 1, "id": "example1a::@306ed2d68c6501e8728f", "jsonFile": "target-example1a-Release-fcad1f7bd502f5a40627.json", "name": "example1a", "projectIndex": 1}, {"directoryIndex": 1, "id": "example1b::@306ed2d68c6501e8728f", "jsonFile": "target-example1b-Release-12a8e42d01476f44d2f5.json", "name": "example1b", "projectIndex": 1}, {"directoryIndex": 1, "id": "example2::@306ed2d68c6501e8728f", "jsonFile": "target-example2-Release-e58dea3cf21f6d3a90d5.json", "name": "example2", "projectIndex": 1}, {"directoryIndex": 1, "id": "example3::@306ed2d68c6501e8728f", "jsonFile": "target-example3-Release-981d0b8876d1bbd2c17d.json", "name": "example3", "projectIndex": 1}, {"directoryIndex": 1, "id": "example3b::@306ed2d68c6501e8728f", "jsonFile": "target-example3b-Release-8086b4b0e7a427403c13.json", "name": "example3b", "projectIndex": 1}, {"directoryIndex": 1, "id": "example4::@306ed2d68c6501e8728f", "jsonFile": "target-example4-Release-780a838e21165e361735.json", "name": "example4", "projectIndex": 1}, {"directoryIndex": 1, "id": "example5::@306ed2d68c6501e8728f", "jsonFile": "target-example5-Release-20aace6e59555f5b0d85.json", "name": "example5", "projectIndex": 1}, {"directoryIndex": 1, "id": "exampleLP::@306ed2d68c6501e8728f", "jsonFile": "target-exampleLP-Release-cf7522044a127bae7b7f.json", "name": "exampleLP", "projectIndex": 1}, {"directoryIndex": 1, "id": "qpOASES::@306ed2d68c6501e8728f", "jsonFile": "target-qpOASES-Release-98e76d659935cf215925.json", "name": "qpOASES", "projectIndex": 1}, {"directoryIndex": 1, "id": "qrecipe::@306ed2d68c6501e8728f", "jsonFile": "target-qrecipe-Release-3149665e5486cc45bd97.json", "name": "qrecipe", "projectIndex": 1}, {"directoryIndex": 1, "id": "qrecipeSchur::@306ed2d68c6501e8728f", "jsonFile": "target-qrecipeSchur-Release-7b4a8ecb0b3b8758fa0d.json", "name": "qrecipeSchur", "projectIndex": 1}, {"directoryIndex": 2, "id": "yaml-cpp::@306ed2d68c6501e8728f", "jsonFile": "target-yaml-cpp-Release-947464e137ce72a444f7.json", "name": "yaml-cpp", "projectIndex": 2}, {"directoryIndex": 3, "id": "yaml-cpp-parse::@cc8e4066b47865546cb0", "jsonFile": "target-yaml-cpp-parse-Release-d3720ce05f26e49ac528.json", "name": "yaml-cpp-parse", "projectIndex": 2}, {"directoryIndex": 3, "id": "yaml-cpp-read::@cc8e4066b47865546cb0", "jsonFile": "target-yaml-cpp-read-Release-bb9328887002872a2131.json", "name": "yaml-cpp-read", "projectIndex": 2}, {"directoryIndex": 3, "id": "yaml-cpp-sandbox::@cc8e4066b47865546cb0", "jsonFile": "target-yaml-cpp-sandbox-Release-b635fca294417f6bc768.json", "name": "yaml-cpp-sandbox", "projectIndex": 2}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release", "source": "/home/<USER>/Documents/tinystrech/controllers/control_task2"}, "version": {"major": 2, "minor": 2}}