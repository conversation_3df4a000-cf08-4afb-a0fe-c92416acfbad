{"inputs": [{"path": "CMakeLists.txt"}, {"isExternal": true, "path": "/home/<USER>/Qt5.12.11/Tools/QtCreator/share/qtcreator/package-manager/auto-setup.cmake"}, {"isGenerated": true, "path": "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/3.20.5/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/3.20.5/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/3.20.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "/usr/lib/cmake/eigen3/Eigen3ConfigVersion.cmake"}, {"isExternal": true, "path": "/usr/lib/cmake/eigen3/Eigen3Config.cmake"}, {"isExternal": true, "path": "/usr/lib/cmake/eigen3/Eigen3Targets.cmake"}, {"isExternal": true, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/CMakeLists.txt"}, {"isExternal": true, "path": "/home/<USER>/Qt5.12.11/Tools/QtCreator/share/qtcreator/package-manager/auto-setup.cmake"}, {"isExternal": true, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/CMakeLists.txt"}, {"isExternal": true, "path": "/home/<USER>/Qt5.12.11/Tools/QtCreator/share/qtcreator/package-manager/auto-setup.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeDependentOption.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CheckCXXCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Internal/CheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CTest.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CTestUseLaunchers.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CTestTargets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/DartConfiguration.tcl.in"}, {"isExternal": true, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/yaml-cpp-config.cmake.in"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/BasicConfigVersion-AnyNewerVersion.cmake.in"}, {"isExternal": true, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/yaml-cpp.pc.in"}, {"isExternal": true, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/util/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release", "source": "/home/<USER>/Documents/tinystrech/controllers/control_task2"}, "version": {"major": 1, "minor": 0}}