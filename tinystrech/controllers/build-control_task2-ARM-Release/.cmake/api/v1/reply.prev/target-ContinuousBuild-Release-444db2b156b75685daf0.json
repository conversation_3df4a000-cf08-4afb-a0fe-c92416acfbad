{"backtrace": 5, "backtraceGraph": {"commands": ["add_custom_target", "include"], "files": ["/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CTestTargets.cmake", "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CTest.cmake", "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/CMakeLists.txt"], "nodes": [{"file": 2}, {"command": 1, "file": 2, "line": 16, "parent": 0}, {"file": 1, "parent": 1}, {"command": 1, "file": 1, "line": 264, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 75, "parent": 4}]}, "folder": {"name": "CTestDashboardTargets"}, "id": "ContinuousBuild::@306ed2d68c6501e8728f", "name": "ContinuousBuild", "paths": {"build": "lib", "source": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp"}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib/CMakeFiles/ContinuousBuild", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib/CMakeFiles/ContinuousBuild.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}