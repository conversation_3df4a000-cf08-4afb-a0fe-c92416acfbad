{"archive": {}, "artifacts": [{"path": "lib/libyaml-cpp.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "INCLUDE_DIRECTORIES", "include_directories", "target_include_directories", "set_target_properties", "target_sources"], "files": ["/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 71, "parent": 0}, {"command": 1, "file": 0, "line": 94, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 7, "parent": 3}, {"command": 3, "file": 1, "line": 12, "parent": 3}, {"command": 3, "file": 1, "line": 29, "parent": 3}, {"command": 3, "file": 1, "line": 31, "parent": 3}, {"command": 3, "file": 1, "line": 32, "parent": 3}, {"command": 4, "file": 0, "line": 81, "parent": 0}, {"command": 5, "file": 0, "line": 89, "parent": 0}, {"command": 6, "file": 0, "line": 115, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=c++11 -O3 -pthread -O3 -DNDEBUG"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 2, "fragment": "-Wextra"}, {"backtrace": 2, "fragment": "-W<PERSON>dow"}, {"backtrace": 2, "fragment": "-Weffc++"}, {"backtrace": 2, "fragment": "-Wno-long-long"}, {"backtrace": 2, "fragment": "-pedantic"}, {"backtrace": 2, "fragment": "-pedantic-errors"}, {"fragment": "-std=gnu++11"}], "defines": [{"define": "__USE_SINGLE_PRECISION__"}], "includes": [{"backtrace": 4, "path": "/usr/include/eigen3"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_vmc"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_wb"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vmc_inc"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vision_location"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/cube_vision"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/include"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils"}, {"backtrace": 6, "path": "/usr/local/include"}, {"backtrace": 7, "path": "/home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include"}, {"backtrace": 8, "path": "/home/<USER>/Downloads/boost_cross/include"}, {"backtrace": 9, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/include"}, {"backtrace": 9, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src"}], "language": "CXX", "languageStandard": {"backtraces": [10], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]}], "id": "yaml-cpp::@306ed2d68c6501e8728f", "name": "yaml-cpp", "nameOnDisk": "libyaml-cpp.a", "paths": {"build": "lib", "source": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]}], "sources": [{"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilder.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilderadapter.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/binary.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/convert.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/depthguard.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emit.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitfromevents.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitter.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterutils.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exceptions.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/memory.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node_data.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/null.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ostream_wrapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parse.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parser.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanscalar.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantag.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantoken.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/simplekey.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/singledocparser.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.cpp", "sourceGroupIndex": 0}, {"backtrace": 11, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}