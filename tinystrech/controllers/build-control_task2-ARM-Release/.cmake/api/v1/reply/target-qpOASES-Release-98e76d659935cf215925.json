{"archive": {}, "artifacts": [{"path": "lib/libs/libqpOASES.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["ADD_LIBRARY", "INSTALL", "INCLUDE_DIRECTORIES", "include_directories"], "files": ["/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 107, "parent": 0}, {"command": 1, "file": 0, "line": 108, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 7, "parent": 3}, {"command": 3, "file": 1, "line": 12, "parent": 3}, {"command": 3, "file": 1, "line": 29, "parent": 3}, {"command": 3, "file": 1, "line": 31, "parent": 3}, {"command": 3, "file": 1, "line": 32, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-std=c++11 -O3 -pthread -D__NO_COPYRIGHT__ -Wall -pedantic -Wfloat-equal -Wshadow -DLINUX -g -O3 -finline-functions"}], "defines": [{"define": "__USE_SINGLE_PRECISION__"}], "includes": [{"backtrace": 4, "path": "/usr/include/eigen3"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_vmc"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/include_wb"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vmc_inc"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/vision_location"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/cube_vision"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/include"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model"}, {"backtrace": 5, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils"}, {"backtrace": 6, "path": "/usr/local/include"}, {"backtrace": 7, "path": "/home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include"}, {"backtrace": 8, "path": "/home/<USER>/Downloads/boost_cross/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "id": "qpOASES::@306ed2d68c6501e8728f", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/usr/local"}}, "name": "qpOASES", "nameOnDisk": "libqpOASES.a", "paths": {"build": "lib", "source": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/BLASReplacement.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/Bounds.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/Constraints.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/Flipper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/Indexlist.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/LAPACKReplacement.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/Matrices.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/MessageHandling.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/OQPinterface.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/Options.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/QProblem.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/QProblemB.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/SQProblem.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/SQProblemSchur.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/SolutionAnalysis.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/SparseSolver.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/SubjectTo.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "/home/<USER>/Documents/tinystrech/controllers/tinystretch/qpOASES/src/Utils.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}