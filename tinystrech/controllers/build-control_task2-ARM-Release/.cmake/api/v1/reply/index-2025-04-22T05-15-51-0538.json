{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake", "cpack": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cpack", "ctest": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/ctest", "root": "/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20"}, "version": {"isDirty": false, "major": 3, "minor": 20, "patch": 5, "string": "3.20.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-aadfb0271f391f148239.json", "kind": "codemodel", "version": {"major": 2, "minor": 2}}, {"jsonFile": "cache-v2-75f34daf12e51656ed38.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-0fca18e28d92e2ceee49.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-75f34daf12e51656ed38.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-0fca18e28d92e2ceee49.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-aadfb0271f391f148239.json", "kind": "codemodel", "version": {"major": 2, "minor": 2}}}}