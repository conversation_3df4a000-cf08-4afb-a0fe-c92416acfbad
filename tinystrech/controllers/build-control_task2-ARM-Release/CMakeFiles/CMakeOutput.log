The system is: Linux - 5.4.0-120-generic - x86_64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"

The C compiler identification is GNU, found in "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/3.20.5/CompilerIdC/a.out"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"

The CXX compiler identification is GNU, found in "/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/3.20.5/CompilerIdCXX/a.out"

Detecting C compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make -f Makefile cmTC_267d2/fast && /usr/bin/make  -f CMakeFiles/cmTC_267d2.dir/build.make CMakeFiles/cmTC_267d2.dir/build
make[1]: Entering directory '/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o
/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc   -v -o CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o -c /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCCompilerABI.c
Using built-in specs.
COLLECT_GCC=/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc
Target: aarch64-linux-gnu
Configured with: ../configure --prefix=/home/<USER>/Desktop/cross-gcc/ --target=aarch64-linux-gnu --enable-languages=c,c++ --disable-multilib
Thread model: posix
gcc version 9.3.0 (GCC) 
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64'
 /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/cc1 -quiet -v /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mlittle-endian -mabi=lp64 -auxbase-strip CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o -version -o /tmp/ccxj3H3A.s
GNU C17 (GCC) version 9.3.0 (aarch64-linux-gnu)
	compiled by GNU C version 7.5.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version none
GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
ignoring nonexistent directory "/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/sys-include"
#include "..." search starts here:
#include <...> search starts here:
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include
End of search list.
GNU C17 (GCC) version 9.3.0 (aarch64-linux-gnu)
	compiled by GNU C version 7.5.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version none
GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
Compiler executable checksum: a6d023c66e1a0c04a57612f8ca6a8e1c
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64'
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o /tmp/ccxj3H3A.s
GNU assembler version 2.34 (aarch64-linux-gnu) using BFD version (GNU Binutils) 2.34
COMPILER_PATH=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/
LIBRARY_PATH=/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64'
Linking C executable cmTC_267d2
/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E cmake_link_script CMakeFiles/cmTC_267d2.dir/link.txt --verbose=1
/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc  -v -rdynamic CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o -o cmTC_267d2 
Using built-in specs.
COLLECT_GCC=/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc
COLLECT_LTO_WRAPPER=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper
Target: aarch64-linux-gnu
Configured with: ../configure --prefix=/home/<USER>/Desktop/cross-gcc/ --target=aarch64-linux-gnu --enable-languages=c,c++ --disable-multilib
Thread model: posix
gcc version 9.3.0 (GCC) 
COMPILER_PATH=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/
LIBRARY_PATH=/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/
COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_267d2' '-mlittle-endian' '-mabi=lp64'
 /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/collect2 -plugin /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/liblto_plugin.so -plugin-opt=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccjT4GmD.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --eh-frame-hdr -export-dynamic -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_267d2 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0 -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64 -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o
COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_267d2' '-mlittle-endian' '-mabi=lp64'
make[1]: Leaving directory '/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp'



Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include]
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed]
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include]
  end of search list found
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include] ==> [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include]
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed] ==> [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed]
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include]
  implicit include dirs: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include;/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(aarch64-linux-gnu-ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/make -f Makefile cmTC_267d2/fast && /usr/bin/make  -f CMakeFiles/cmTC_267d2.dir/build.make CMakeFiles/cmTC_267d2.dir/build]
  ignore line: [make[1]: Entering directory '/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp']
  ignore line: [Building C object CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o]
  ignore line: [/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc   -v -o CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o -c /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCCompilerABI.c]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc]
  ignore line: [Target: aarch64-linux-gnu]
  ignore line: [Configured with: ../configure --prefix=/home/<USER>/Desktop/cross-gcc/ --target=aarch64-linux-gnu --enable-languages=c c++ --disable-multilib]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 9.3.0 (GCC) ]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64']
  ignore line: [ /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/cc1 -quiet -v /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCCompilerABI.c -quiet -dumpbase CMakeCCompilerABI.c -mlittle-endian -mabi=lp64 -auxbase-strip CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o -version -o /tmp/ccxj3H3A.s]
  ignore line: [GNU C17 (GCC) version 9.3.0 (aarch64-linux-gnu)]
  ignore line: [	compiled by GNU C version 7.5.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version none]
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring nonexistent directory "/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/sys-include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include]
  ignore line: [End of search list.]
  ignore line: [GNU C17 (GCC) version 9.3.0 (aarch64-linux-gnu)]
  ignore line: [	compiled by GNU C version 7.5.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version none]
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [Compiler executable checksum: a6d023c66e1a0c04a57612f8ca6a8e1c]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64']
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o /tmp/ccxj3H3A.s]
  ignore line: [GNU assembler version 2.34 (aarch64-linux-gnu) using BFD version (GNU Binutils) 2.34]
  ignore line: [COMPILER_PATH=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/]
  ignore line: [LIBRARY_PATH=/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o' '-c' '-mlittle-endian' '-mabi=lp64']
  ignore line: [Linking C executable cmTC_267d2]
  ignore line: [/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E cmake_link_script CMakeFiles/cmTC_267d2.dir/link.txt --verbose=1]
  ignore line: [/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc  -v -rdynamic CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o -o cmTC_267d2 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc]
  ignore line: [COLLECT_LTO_WRAPPER=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper]
  ignore line: [Target: aarch64-linux-gnu]
  ignore line: [Configured with: ../configure --prefix=/home/<USER>/Desktop/cross-gcc/ --target=aarch64-linux-gnu --enable-languages=c c++ --disable-multilib]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 9.3.0 (GCC) ]
  ignore line: [COMPILER_PATH=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/]
  ignore line: [LIBRARY_PATH=/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_267d2' '-mlittle-endian' '-mabi=lp64']
  link line: [ /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/collect2 -plugin /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/liblto_plugin.so -plugin-opt=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccjT4GmD.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_s --eh-frame-hdr -export-dynamic -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_267d2 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0 -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64 -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o -lgcc --push-state --as-needed -lgcc_s --pop-state -lc -lgcc --push-state --as-needed -lgcc_s --pop-state /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o]
    arg [/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccjT4GmD.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-export-dynamic] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib/ld-linux-aarch64.so.1] ==> ignore
    arg [-X] ==> ignore
    arg [-EL] ==> ignore
    arg [-maarch64linux] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_267d2] ==> ignore
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o]
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o]
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o]
    arg [-L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0] ==> dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0]
    arg [-L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64] ==> dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64]
    arg [-L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib] ==> dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib]
    arg [CMakeFiles/cmTC_267d2.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-lgcc] ==> lib [gcc]
    arg [--push-state] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--pop-state] ==> ignore
    arg [-lc] ==> lib [c]
    arg [-lgcc] ==> lib [gcc]
    arg [--push-state] ==> ignore
    arg [--as-needed] ==> ignore
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [--pop-state] ==> ignore
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o]
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o]
  collapse obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crt1.o]
  collapse obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crti.o]
  collapse obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crtn.o]
  collapse library dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0] ==> [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0]
  collapse library dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib64]
  collapse library dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib]
  implicit libs: [gcc;gcc_s;c;gcc;gcc_s]
  implicit objs: [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crt1.o;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crti.o;/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o;/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crtn.o]
  implicit dirs: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib64;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make -f Makefile cmTC_5c83e/fast && /usr/bin/make  -f CMakeFiles/cmTC_5c83e.dir/build.make CMakeFiles/cmTC_5c83e.dir/build
make[1]: Entering directory '/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o
/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++   -v -o CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o -c /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCXXCompilerABI.cpp
Using built-in specs.
COLLECT_GCC=/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++
Target: aarch64-linux-gnu
Configured with: ../configure --prefix=/home/<USER>/Desktop/cross-gcc/ --target=aarch64-linux-gnu --enable-languages=c,c++ --disable-multilib
Thread model: posix
gcc version 9.3.0 (GCC) 
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
 /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/cc1plus -quiet -v -D_GNU_SOURCE /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mlittle-endian -mabi=lp64 -auxbase-strip CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o -version -o /tmp/ccJTjcmF.s
GNU C++14 (GCC) version 9.3.0 (aarch64-linux-gnu)
	compiled by GNU C version 7.5.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version none
GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
ignoring nonexistent directory "/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/sys-include"
#include "..." search starts here:
#include <...> search starts here:
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0/backward
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include
End of search list.
GNU C++14 (GCC) version 9.3.0 (aarch64-linux-gnu)
	compiled by GNU C version 7.5.0, GMP version 6.1.2, MPFR version 4.0.1, MPC version 1.1.0, isl version none
GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
Compiler executable checksum: 1c705682e1a5517eeafbeb8ba8b1bd5e
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccJTjcmF.s
GNU assembler version 2.34 (aarch64-linux-gnu) using BFD version (GNU Binutils) 2.34
COMPILER_PATH=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/
LIBRARY_PATH=/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/
COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
Linking CXX executable cmTC_5c83e
/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5c83e.dir/link.txt --verbose=1
/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++  -v -rdynamic CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_5c83e 
Using built-in specs.
COLLECT_GCC=/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++
COLLECT_LTO_WRAPPER=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper
Target: aarch64-linux-gnu
Configured with: ../configure --prefix=/home/<USER>/Desktop/cross-gcc/ --target=aarch64-linux-gnu --enable-languages=c,c++ --disable-multilib
Thread model: posix
gcc version 9.3.0 (GCC) 
COMPILER_PATH=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/
LIBRARY_PATH=/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/
COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_5c83e' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
 /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/collect2 -plugin /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/liblto_plugin.so -plugin-opt=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccsEudiI.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --eh-frame-hdr -export-dynamic -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_5c83e /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0 -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64 -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o
COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_5c83e' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64'
make[1]: Leaving directory '/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp'



Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0]
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu]
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0/backward]
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include]
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed]
    add: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include]
  end of search list found
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0]
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu]
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0/backward] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward]
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include] ==> [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include]
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed] ==> [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed]
  collapse include dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include]
  implicit include dirs: [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward;/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include;/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(aarch64-linux-gnu-ld|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):/usr/bin/make -f Makefile cmTC_5c83e/fast && /usr/bin/make  -f CMakeFiles/cmTC_5c83e.dir/build.make CMakeFiles/cmTC_5c83e.dir/build]
  ignore line: [make[1]: Entering directory '/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/CMakeTmp']
  ignore line: [Building CXX object CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++   -v -o CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o -c /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCXXCompilerABI.cpp]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++]
  ignore line: [Target: aarch64-linux-gnu]
  ignore line: [Configured with: ../configure --prefix=/home/<USER>/Desktop/cross-gcc/ --target=aarch64-linux-gnu --enable-languages=c c++ --disable-multilib]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 9.3.0 (GCC) ]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
  ignore line: [ /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/cc1plus -quiet -v -D_GNU_SOURCE /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/share/cmake-3.20/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpbase CMakeCXXCompilerABI.cpp -mlittle-endian -mabi=lp64 -auxbase-strip CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o -version -o /tmp/ccJTjcmF.s]
  ignore line: [GNU C++14 (GCC) version 9.3.0 (aarch64-linux-gnu)]
  ignore line: [	compiled by GNU C version 7.5.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version none]
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [ignoring nonexistent directory "/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/sys-include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include/c++/9.3.0/backward]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include-fixed]
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/include]
  ignore line: [End of search list.]
  ignore line: [GNU C++14 (GCC) version 9.3.0 (aarch64-linux-gnu)]
  ignore line: [	compiled by GNU C version 7.5.0  GMP version 6.1.2  MPFR version 4.0.1  MPC version 1.1.0  isl version none]
  ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
  ignore line: [Compiler executable checksum: 1c705682e1a5517eeafbeb8ba8b1bd5e]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
  ignore line: [ /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/as -v -EL -mabi=lp64 -o CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o /tmp/ccJTjcmF.s]
  ignore line: [GNU assembler version 2.34 (aarch64-linux-gnu) using BFD version (GNU Binutils) 2.34]
  ignore line: [COMPILER_PATH=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/]
  ignore line: [LIBRARY_PATH=/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o' '-c' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
  ignore line: [Linking CXX executable cmTC_5c83e]
  ignore line: [/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E cmake_link_script CMakeFiles/cmTC_5c83e.dir/link.txt --verbose=1]
  ignore line: [/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++  -v -rdynamic CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_5c83e ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++]
  ignore line: [COLLECT_LTO_WRAPPER=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper]
  ignore line: [Target: aarch64-linux-gnu]
  ignore line: [Configured with: ../configure --prefix=/home/<USER>/Desktop/cross-gcc/ --target=aarch64-linux-gnu --enable-languages=c c++ --disable-multilib]
  ignore line: [Thread model: posix]
  ignore line: [gcc version 9.3.0 (GCC) ]
  ignore line: [COMPILER_PATH=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/bin/]
  ignore line: [LIBRARY_PATH=/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64/:/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-rdynamic' '-o' 'cmTC_5c83e' '-shared-libgcc' '-mlittle-endian' '-mabi=lp64']
  link line: [ /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/collect2 -plugin /home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/liblto_plugin.so -plugin-opt=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper -plugin-opt=-fresolution=/tmp/ccsEudiI.res -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc --eh-frame-hdr -export-dynamic -dynamic-linker /lib/ld-linux-aarch64.so.1 -X -EL -maarch64linux -o cmTC_5c83e /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0 -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64 -L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o]
    arg [/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/collect2] ==> ignore
    arg [-plugin] ==> ignore
    arg [/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/liblto_plugin.so] ==> ignore
    arg [-plugin-opt=/home/<USER>/Desktop/cross-gcc/libexec/gcc/aarch64-linux-gnu/9.3.0/lto-wrapper] ==> ignore
    arg [-plugin-opt=-fresolution=/tmp/ccsEudiI.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-export-dynamic] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/lib/ld-linux-aarch64.so.1] ==> ignore
    arg [-X] ==> ignore
    arg [-EL] ==> ignore
    arg [-maarch64linux] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_5c83e] ==> ignore
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o]
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o]
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o]
    arg [-L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0] ==> dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0]
    arg [-L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64] ==> dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64]
    arg [-L/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib] ==> dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib]
    arg [CMakeFiles/cmTC_5c83e.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lm] ==> lib [m]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lc] ==> lib [c]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o]
    arg [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o] ==> obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o]
  collapse obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crt1.o] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crt1.o]
  collapse obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crti.o] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crti.o]
  collapse obj [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/crtn.o] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crtn.o]
  collapse library dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0] ==> [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0]
  collapse library dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib/../lib64] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib64]
  collapse library dir [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/../../../../aarch64-linux-gnu/lib] ==> [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib]
  implicit libs: [stdc++;m;gcc_s;gcc;c;gcc_s;gcc]
  implicit objs: [/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crt1.o;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crti.o;/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtbegin.o;/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/crtend.o;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib/crtn.o]
  implicit dirs: [/home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib64;/home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/lib]
  implicit fwks: []


