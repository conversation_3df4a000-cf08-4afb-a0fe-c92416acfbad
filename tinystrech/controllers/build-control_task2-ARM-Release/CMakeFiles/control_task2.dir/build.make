# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/tinystrech/controllers/control_task2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release

# Include any dependencies generated for this target.
include CMakeFiles/control_task2.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/control_task2.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/control_task2.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/control_task2.dir/flags.make

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.s

CMakeFiles/control_task2.dir/src/butler.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/src/butler.cpp.o: /home/<USER>/Documents/tinystrech/controllers/control_task2/src/butler.cpp
CMakeFiles/control_task2.dir/src/butler.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object CMakeFiles/control_task2.dir/src/butler.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/src/butler.cpp.o -MF CMakeFiles/control_task2.dir/src/butler.cpp.o.d -o CMakeFiles/control_task2.dir/src/butler.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/control_task2/src/butler.cpp

CMakeFiles/control_task2.dir/src/butler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/src/butler.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/control_task2/src/butler.cpp > CMakeFiles/control_task2.dir/src/butler.cpp.i

CMakeFiles/control_task2.dir/src/butler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/src/butler.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/control_task2/src/butler.cpp -o CMakeFiles/control_task2.dir/src/butler.cpp.s

CMakeFiles/control_task2.dir/src/main.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/src/main.cpp.o: /home/<USER>/Documents/tinystrech/controllers/control_task2/src/main.cpp
CMakeFiles/control_task2.dir/src/main.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object CMakeFiles/control_task2.dir/src/main.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/src/main.cpp.o -MF CMakeFiles/control_task2.dir/src/main.cpp.o.d -o CMakeFiles/control_task2.dir/src/main.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/control_task2/src/main.cpp

CMakeFiles/control_task2.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/src/main.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/control_task2/src/main.cpp > CMakeFiles/control_task2.dir/src/main.cpp.i

CMakeFiles/control_task2.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/src/main.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/control_task2/src/main.cpp -o CMakeFiles/control_task2.dir/src/main.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.s

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o: CMakeFiles/control_task2.dir/flags.make
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp
CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o: CMakeFiles/control_task2.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building CXX object CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o -MF CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o.d -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.i"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp > CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.i

CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.s"
	/home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp -o CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.s

# Object files for target control_task2
control_task2_OBJECTS = \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o" \
"CMakeFiles/control_task2.dir/src/butler.cpp.o" \
"CMakeFiles/control_task2.dir/src/main.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o" \
"CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o"

# External object files for target control_task2
control_task2_EXTERNAL_OBJECTS =

/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/self_right_qt.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/gait_src/stand_qt.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/action_skill.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/adrc.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/auto_mission.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/force_imp_controller.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/hardware_interface.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/locomotion_sfm.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/state_estimator.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/locomotion_src/vmc_controller.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/FootSwingTrajectory.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/PositionVelocityEstimator.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/RT_math.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/common_math.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/eso_qt.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/fliter_math.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/ground_est_qt.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_pino.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/kin_math_rbdl.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/log_pinRefined.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/optimize_qt.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetInf.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rtGetNaN.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/rt_nonfinite.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/traj_math.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/vision_locomotion.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/math_src/zmp_math.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/RobotState.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/SolverMPC.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/convexMPC_interface.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/mpc/mpc_locomotion.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/comm.c.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/memory_share.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/share_mem/sys_timer.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/src/butler.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/src/main.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/joint.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/link.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/model.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/pose.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinystr.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlparser.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Constraints.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Dynamics.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Joint.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Kinematics.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Logging.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/Model.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_mathutils.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_utils.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/rbdl_version.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/rbdl/urdfreader.cc.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/DynModel.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/KinModel.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/model/RobotModel.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/common_utils/common.cpp.o
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/build.make
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: lib/libs/libqpOASES.a
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: lib/libyaml-cpp.a
/home/<USER>/Documents/tinystrech/controllers/build/control_task2: CMakeFiles/control_task2.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Linking CXX executable /home/<USER>/Documents/tinystrech/controllers/build/control_task2"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/control_task2.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/control_task2.dir/build: /home/<USER>/Documents/tinystrech/controllers/build/control_task2
.PHONY : CMakeFiles/control_task2.dir/build

CMakeFiles/control_task2.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/control_task2.dir/cmake_clean.cmake
.PHONY : CMakeFiles/control_task2.dir/clean

CMakeFiles/control_task2.dir/depend:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/tinystrech/controllers/control_task2 /home/<USER>/Documents/tinystrech/controllers/control_task2 /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/control_task2.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/control_task2.dir/depend

