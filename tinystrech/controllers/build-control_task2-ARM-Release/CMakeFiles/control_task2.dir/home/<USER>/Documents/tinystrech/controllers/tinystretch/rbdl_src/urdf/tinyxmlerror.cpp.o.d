CMakeFiles/control_task2.dir/home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp.o: \
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxmlerror.cpp \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h \
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/rbdl_src/urdf/tinyxml.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h \
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h \
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdlib.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/string.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/strings.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h \
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream \
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
