# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/tinystrech/controllers/control_task2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib//CMakeFiles/progress.marks
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -P /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/VerifyGlobs.cmake
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
lib/CMakeFiles/yaml-cpp.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/yaml-cpp.dir/rule
.PHONY : lib/CMakeFiles/yaml-cpp.dir/rule

# Convenience name for target.
yaml-cpp: lib/CMakeFiles/yaml-cpp.dir/rule
.PHONY : yaml-cpp

# fast build rule for target.
yaml-cpp/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/build
.PHONY : yaml-cpp/fast

# Convenience name for target.
lib/CMakeFiles/ContinuousSubmit.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ContinuousSubmit.dir/rule
.PHONY : lib/CMakeFiles/ContinuousSubmit.dir/rule

# Convenience name for target.
ContinuousSubmit: lib/CMakeFiles/ContinuousSubmit.dir/rule
.PHONY : ContinuousSubmit

# fast build rule for target.
ContinuousSubmit/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ContinuousSubmit.dir/build.make lib/CMakeFiles/ContinuousSubmit.dir/build
.PHONY : ContinuousSubmit/fast

# Convenience name for target.
lib/CMakeFiles/ContinuousCoverage.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ContinuousCoverage.dir/rule
.PHONY : lib/CMakeFiles/ContinuousCoverage.dir/rule

# Convenience name for target.
ContinuousCoverage: lib/CMakeFiles/ContinuousCoverage.dir/rule
.PHONY : ContinuousCoverage

# fast build rule for target.
ContinuousCoverage/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ContinuousCoverage.dir/build.make lib/CMakeFiles/ContinuousCoverage.dir/build
.PHONY : ContinuousCoverage/fast

# Convenience name for target.
lib/CMakeFiles/ContinuousTest.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ContinuousTest.dir/rule
.PHONY : lib/CMakeFiles/ContinuousTest.dir/rule

# Convenience name for target.
ContinuousTest: lib/CMakeFiles/ContinuousTest.dir/rule
.PHONY : ContinuousTest

# fast build rule for target.
ContinuousTest/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ContinuousTest.dir/build.make lib/CMakeFiles/ContinuousTest.dir/build
.PHONY : ContinuousTest/fast

# Convenience name for target.
lib/CMakeFiles/ContinuousBuild.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ContinuousBuild.dir/rule
.PHONY : lib/CMakeFiles/ContinuousBuild.dir/rule

# Convenience name for target.
ContinuousBuild: lib/CMakeFiles/ContinuousBuild.dir/rule
.PHONY : ContinuousBuild

# fast build rule for target.
ContinuousBuild/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ContinuousBuild.dir/build.make lib/CMakeFiles/ContinuousBuild.dir/build
.PHONY : ContinuousBuild/fast

# Convenience name for target.
lib/CMakeFiles/ContinuousMemCheck.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ContinuousMemCheck.dir/rule
.PHONY : lib/CMakeFiles/ContinuousMemCheck.dir/rule

# Convenience name for target.
ContinuousMemCheck: lib/CMakeFiles/ContinuousMemCheck.dir/rule
.PHONY : ContinuousMemCheck

# fast build rule for target.
ContinuousMemCheck/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ContinuousMemCheck.dir/build.make lib/CMakeFiles/ContinuousMemCheck.dir/build
.PHONY : ContinuousMemCheck/fast

# Convenience name for target.
lib/CMakeFiles/Nightly.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/Nightly.dir/rule
.PHONY : lib/CMakeFiles/Nightly.dir/rule

# Convenience name for target.
Nightly: lib/CMakeFiles/Nightly.dir/rule
.PHONY : Nightly

# fast build rule for target.
Nightly/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/Nightly.dir/build.make lib/CMakeFiles/Nightly.dir/build
.PHONY : Nightly/fast

# Convenience name for target.
lib/CMakeFiles/NightlyTest.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlyTest.dir/rule
.PHONY : lib/CMakeFiles/NightlyTest.dir/rule

# Convenience name for target.
NightlyTest: lib/CMakeFiles/NightlyTest.dir/rule
.PHONY : NightlyTest

# fast build rule for target.
NightlyTest/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlyTest.dir/build.make lib/CMakeFiles/NightlyTest.dir/build
.PHONY : NightlyTest/fast

# Convenience name for target.
lib/CMakeFiles/NightlyUpdate.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlyUpdate.dir/rule
.PHONY : lib/CMakeFiles/NightlyUpdate.dir/rule

# Convenience name for target.
NightlyUpdate: lib/CMakeFiles/NightlyUpdate.dir/rule
.PHONY : NightlyUpdate

# fast build rule for target.
NightlyUpdate/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlyUpdate.dir/build.make lib/CMakeFiles/NightlyUpdate.dir/build
.PHONY : NightlyUpdate/fast

# Convenience name for target.
lib/CMakeFiles/Continuous.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/Continuous.dir/rule
.PHONY : lib/CMakeFiles/Continuous.dir/rule

# Convenience name for target.
Continuous: lib/CMakeFiles/Continuous.dir/rule
.PHONY : Continuous

# fast build rule for target.
Continuous/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/Continuous.dir/build.make lib/CMakeFiles/Continuous.dir/build
.PHONY : Continuous/fast

# Convenience name for target.
lib/CMakeFiles/NightlyBuild.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlyBuild.dir/rule
.PHONY : lib/CMakeFiles/NightlyBuild.dir/rule

# Convenience name for target.
NightlyBuild: lib/CMakeFiles/NightlyBuild.dir/rule
.PHONY : NightlyBuild

# fast build rule for target.
NightlyBuild/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlyBuild.dir/build.make lib/CMakeFiles/NightlyBuild.dir/build
.PHONY : NightlyBuild/fast

# Convenience name for target.
lib/CMakeFiles/NightlyStart.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlyStart.dir/rule
.PHONY : lib/CMakeFiles/NightlyStart.dir/rule

# Convenience name for target.
NightlyStart: lib/CMakeFiles/NightlyStart.dir/rule
.PHONY : NightlyStart

# fast build rule for target.
NightlyStart/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlyStart.dir/build.make lib/CMakeFiles/NightlyStart.dir/build
.PHONY : NightlyStart/fast

# Convenience name for target.
lib/CMakeFiles/NightlyMemoryCheck.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlyMemoryCheck.dir/rule
.PHONY : lib/CMakeFiles/NightlyMemoryCheck.dir/rule

# Convenience name for target.
NightlyMemoryCheck: lib/CMakeFiles/NightlyMemoryCheck.dir/rule
.PHONY : NightlyMemoryCheck

# fast build rule for target.
NightlyMemoryCheck/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlyMemoryCheck.dir/build.make lib/CMakeFiles/NightlyMemoryCheck.dir/build
.PHONY : NightlyMemoryCheck/fast

# Convenience name for target.
lib/CMakeFiles/NightlyMemCheck.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlyMemCheck.dir/rule
.PHONY : lib/CMakeFiles/NightlyMemCheck.dir/rule

# Convenience name for target.
NightlyMemCheck: lib/CMakeFiles/NightlyMemCheck.dir/rule
.PHONY : NightlyMemCheck

# fast build rule for target.
NightlyMemCheck/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlyMemCheck.dir/build.make lib/CMakeFiles/NightlyMemCheck.dir/build
.PHONY : NightlyMemCheck/fast

# Convenience name for target.
lib/CMakeFiles/ExperimentalStart.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ExperimentalStart.dir/rule
.PHONY : lib/CMakeFiles/ExperimentalStart.dir/rule

# Convenience name for target.
ExperimentalStart: lib/CMakeFiles/ExperimentalStart.dir/rule
.PHONY : ExperimentalStart

# fast build rule for target.
ExperimentalStart/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ExperimentalStart.dir/build.make lib/CMakeFiles/ExperimentalStart.dir/build
.PHONY : ExperimentalStart/fast

# Convenience name for target.
lib/CMakeFiles/ContinuousConfigure.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ContinuousConfigure.dir/rule
.PHONY : lib/CMakeFiles/ContinuousConfigure.dir/rule

# Convenience name for target.
ContinuousConfigure: lib/CMakeFiles/ContinuousConfigure.dir/rule
.PHONY : ContinuousConfigure

# fast build rule for target.
ContinuousConfigure/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ContinuousConfigure.dir/build.make lib/CMakeFiles/ContinuousConfigure.dir/build
.PHONY : ContinuousConfigure/fast

# Convenience name for target.
lib/CMakeFiles/NightlyCoverage.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlyCoverage.dir/rule
.PHONY : lib/CMakeFiles/NightlyCoverage.dir/rule

# Convenience name for target.
NightlyCoverage: lib/CMakeFiles/NightlyCoverage.dir/rule
.PHONY : NightlyCoverage

# fast build rule for target.
NightlyCoverage/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlyCoverage.dir/build.make lib/CMakeFiles/NightlyCoverage.dir/build
.PHONY : NightlyCoverage/fast

# Convenience name for target.
lib/CMakeFiles/ExperimentalUpdate.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ExperimentalUpdate.dir/rule
.PHONY : lib/CMakeFiles/ExperimentalUpdate.dir/rule

# Convenience name for target.
ExperimentalUpdate: lib/CMakeFiles/ExperimentalUpdate.dir/rule
.PHONY : ExperimentalUpdate

# fast build rule for target.
ExperimentalUpdate/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ExperimentalUpdate.dir/build.make lib/CMakeFiles/ExperimentalUpdate.dir/build
.PHONY : ExperimentalUpdate/fast

# Convenience name for target.
lib/CMakeFiles/ExperimentalConfigure.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ExperimentalConfigure.dir/rule
.PHONY : lib/CMakeFiles/ExperimentalConfigure.dir/rule

# Convenience name for target.
ExperimentalConfigure: lib/CMakeFiles/ExperimentalConfigure.dir/rule
.PHONY : ExperimentalConfigure

# fast build rule for target.
ExperimentalConfigure/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ExperimentalConfigure.dir/build.make lib/CMakeFiles/ExperimentalConfigure.dir/build
.PHONY : ExperimentalConfigure/fast

# Convenience name for target.
lib/CMakeFiles/ExperimentalCoverage.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ExperimentalCoverage.dir/rule
.PHONY : lib/CMakeFiles/ExperimentalCoverage.dir/rule

# Convenience name for target.
ExperimentalCoverage: lib/CMakeFiles/ExperimentalCoverage.dir/rule
.PHONY : ExperimentalCoverage

# fast build rule for target.
ExperimentalCoverage/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ExperimentalCoverage.dir/build.make lib/CMakeFiles/ExperimentalCoverage.dir/build
.PHONY : ExperimentalCoverage/fast

# Convenience name for target.
lib/CMakeFiles/ExperimentalBuild.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ExperimentalBuild.dir/rule
.PHONY : lib/CMakeFiles/ExperimentalBuild.dir/rule

# Convenience name for target.
ExperimentalBuild: lib/CMakeFiles/ExperimentalBuild.dir/rule
.PHONY : ExperimentalBuild

# fast build rule for target.
ExperimentalBuild/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ExperimentalBuild.dir/build.make lib/CMakeFiles/ExperimentalBuild.dir/build
.PHONY : ExperimentalBuild/fast

# Convenience name for target.
lib/CMakeFiles/NightlyConfigure.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlyConfigure.dir/rule
.PHONY : lib/CMakeFiles/NightlyConfigure.dir/rule

# Convenience name for target.
NightlyConfigure: lib/CMakeFiles/NightlyConfigure.dir/rule
.PHONY : NightlyConfigure

# fast build rule for target.
NightlyConfigure/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlyConfigure.dir/build.make lib/CMakeFiles/NightlyConfigure.dir/build
.PHONY : NightlyConfigure/fast

# Convenience name for target.
lib/CMakeFiles/ExperimentalTest.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ExperimentalTest.dir/rule
.PHONY : lib/CMakeFiles/ExperimentalTest.dir/rule

# Convenience name for target.
ExperimentalTest: lib/CMakeFiles/ExperimentalTest.dir/rule
.PHONY : ExperimentalTest

# fast build rule for target.
ExperimentalTest/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ExperimentalTest.dir/build.make lib/CMakeFiles/ExperimentalTest.dir/build
.PHONY : ExperimentalTest/fast

# Convenience name for target.
lib/CMakeFiles/ExperimentalMemCheck.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ExperimentalMemCheck.dir/rule
.PHONY : lib/CMakeFiles/ExperimentalMemCheck.dir/rule

# Convenience name for target.
ExperimentalMemCheck: lib/CMakeFiles/ExperimentalMemCheck.dir/rule
.PHONY : ExperimentalMemCheck

# fast build rule for target.
ExperimentalMemCheck/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ExperimentalMemCheck.dir/build.make lib/CMakeFiles/ExperimentalMemCheck.dir/build
.PHONY : ExperimentalMemCheck/fast

# Convenience name for target.
lib/CMakeFiles/Experimental.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/Experimental.dir/rule
.PHONY : lib/CMakeFiles/Experimental.dir/rule

# Convenience name for target.
Experimental: lib/CMakeFiles/Experimental.dir/rule
.PHONY : Experimental

# fast build rule for target.
Experimental/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/Experimental.dir/build.make lib/CMakeFiles/Experimental.dir/build
.PHONY : Experimental/fast

# Convenience name for target.
lib/CMakeFiles/NightlySubmit.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/NightlySubmit.dir/rule
.PHONY : lib/CMakeFiles/NightlySubmit.dir/rule

# Convenience name for target.
NightlySubmit: lib/CMakeFiles/NightlySubmit.dir/rule
.PHONY : NightlySubmit

# fast build rule for target.
NightlySubmit/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/NightlySubmit.dir/build.make lib/CMakeFiles/NightlySubmit.dir/build
.PHONY : NightlySubmit/fast

# Convenience name for target.
lib/CMakeFiles/ExperimentalSubmit.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ExperimentalSubmit.dir/rule
.PHONY : lib/CMakeFiles/ExperimentalSubmit.dir/rule

# Convenience name for target.
ExperimentalSubmit: lib/CMakeFiles/ExperimentalSubmit.dir/rule
.PHONY : ExperimentalSubmit

# fast build rule for target.
ExperimentalSubmit/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ExperimentalSubmit.dir/build.make lib/CMakeFiles/ExperimentalSubmit.dir/build
.PHONY : ExperimentalSubmit/fast

# Convenience name for target.
lib/CMakeFiles/ContinuousStart.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ContinuousStart.dir/rule
.PHONY : lib/CMakeFiles/ContinuousStart.dir/rule

# Convenience name for target.
ContinuousStart: lib/CMakeFiles/ContinuousStart.dir/rule
.PHONY : ContinuousStart

# fast build rule for target.
ContinuousStart/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ContinuousStart.dir/build.make lib/CMakeFiles/ContinuousStart.dir/build
.PHONY : ContinuousStart/fast

# Convenience name for target.
lib/CMakeFiles/ContinuousUpdate.dir/rule:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/ContinuousUpdate.dir/rule
.PHONY : lib/CMakeFiles/ContinuousUpdate.dir/rule

# Convenience name for target.
ContinuousUpdate: lib/CMakeFiles/ContinuousUpdate.dir/rule
.PHONY : ContinuousUpdate

# fast build rule for target.
ContinuousUpdate/fast:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/ContinuousUpdate.dir/build.make lib/CMakeFiles/ContinuousUpdate.dir/build
.PHONY : ContinuousUpdate/fast

src/binary.o: src/binary.cpp.o
.PHONY : src/binary.o

# target to build an object file
src/binary.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.o
.PHONY : src/binary.cpp.o

src/binary.i: src/binary.cpp.i
.PHONY : src/binary.i

# target to preprocess a source file
src/binary.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.i
.PHONY : src/binary.cpp.i

src/binary.s: src/binary.cpp.s
.PHONY : src/binary.s

# target to generate assembly for a file
src/binary.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.s
.PHONY : src/binary.cpp.s

src/contrib/graphbuilder.o: src/contrib/graphbuilder.cpp.o
.PHONY : src/contrib/graphbuilder.o

# target to build an object file
src/contrib/graphbuilder.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o
.PHONY : src/contrib/graphbuilder.cpp.o

src/contrib/graphbuilder.i: src/contrib/graphbuilder.cpp.i
.PHONY : src/contrib/graphbuilder.i

# target to preprocess a source file
src/contrib/graphbuilder.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.i
.PHONY : src/contrib/graphbuilder.cpp.i

src/contrib/graphbuilder.s: src/contrib/graphbuilder.cpp.s
.PHONY : src/contrib/graphbuilder.s

# target to generate assembly for a file
src/contrib/graphbuilder.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.s
.PHONY : src/contrib/graphbuilder.cpp.s

src/contrib/graphbuilderadapter.o: src/contrib/graphbuilderadapter.cpp.o
.PHONY : src/contrib/graphbuilderadapter.o

# target to build an object file
src/contrib/graphbuilderadapter.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o
.PHONY : src/contrib/graphbuilderadapter.cpp.o

src/contrib/graphbuilderadapter.i: src/contrib/graphbuilderadapter.cpp.i
.PHONY : src/contrib/graphbuilderadapter.i

# target to preprocess a source file
src/contrib/graphbuilderadapter.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.i
.PHONY : src/contrib/graphbuilderadapter.cpp.i

src/contrib/graphbuilderadapter.s: src/contrib/graphbuilderadapter.cpp.s
.PHONY : src/contrib/graphbuilderadapter.s

# target to generate assembly for a file
src/contrib/graphbuilderadapter.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.s
.PHONY : src/contrib/graphbuilderadapter.cpp.s

src/convert.o: src/convert.cpp.o
.PHONY : src/convert.o

# target to build an object file
src/convert.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.o
.PHONY : src/convert.cpp.o

src/convert.i: src/convert.cpp.i
.PHONY : src/convert.i

# target to preprocess a source file
src/convert.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.i
.PHONY : src/convert.cpp.i

src/convert.s: src/convert.cpp.s
.PHONY : src/convert.s

# target to generate assembly for a file
src/convert.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.s
.PHONY : src/convert.cpp.s

src/depthguard.o: src/depthguard.cpp.o
.PHONY : src/depthguard.o

# target to build an object file
src/depthguard.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o
.PHONY : src/depthguard.cpp.o

src/depthguard.i: src/depthguard.cpp.i
.PHONY : src/depthguard.i

# target to preprocess a source file
src/depthguard.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.i
.PHONY : src/depthguard.cpp.i

src/depthguard.s: src/depthguard.cpp.s
.PHONY : src/depthguard.s

# target to generate assembly for a file
src/depthguard.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.s
.PHONY : src/depthguard.cpp.s

src/directives.o: src/directives.cpp.o
.PHONY : src/directives.o

# target to build an object file
src/directives.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.o
.PHONY : src/directives.cpp.o

src/directives.i: src/directives.cpp.i
.PHONY : src/directives.i

# target to preprocess a source file
src/directives.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.i
.PHONY : src/directives.cpp.i

src/directives.s: src/directives.cpp.s
.PHONY : src/directives.s

# target to generate assembly for a file
src/directives.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.s
.PHONY : src/directives.cpp.s

src/emit.o: src/emit.cpp.o
.PHONY : src/emit.o

# target to build an object file
src/emit.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.o
.PHONY : src/emit.cpp.o

src/emit.i: src/emit.cpp.i
.PHONY : src/emit.i

# target to preprocess a source file
src/emit.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.i
.PHONY : src/emit.cpp.i

src/emit.s: src/emit.cpp.s
.PHONY : src/emit.s

# target to generate assembly for a file
src/emit.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.s
.PHONY : src/emit.cpp.s

src/emitfromevents.o: src/emitfromevents.cpp.o
.PHONY : src/emitfromevents.o

# target to build an object file
src/emitfromevents.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o
.PHONY : src/emitfromevents.cpp.o

src/emitfromevents.i: src/emitfromevents.cpp.i
.PHONY : src/emitfromevents.i

# target to preprocess a source file
src/emitfromevents.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.i
.PHONY : src/emitfromevents.cpp.i

src/emitfromevents.s: src/emitfromevents.cpp.s
.PHONY : src/emitfromevents.s

# target to generate assembly for a file
src/emitfromevents.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.s
.PHONY : src/emitfromevents.cpp.s

src/emitter.o: src/emitter.cpp.o
.PHONY : src/emitter.o

# target to build an object file
src/emitter.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o
.PHONY : src/emitter.cpp.o

src/emitter.i: src/emitter.cpp.i
.PHONY : src/emitter.i

# target to preprocess a source file
src/emitter.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.i
.PHONY : src/emitter.cpp.i

src/emitter.s: src/emitter.cpp.s
.PHONY : src/emitter.s

# target to generate assembly for a file
src/emitter.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.s
.PHONY : src/emitter.cpp.s

src/emitterstate.o: src/emitterstate.cpp.o
.PHONY : src/emitterstate.o

# target to build an object file
src/emitterstate.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o
.PHONY : src/emitterstate.cpp.o

src/emitterstate.i: src/emitterstate.cpp.i
.PHONY : src/emitterstate.i

# target to preprocess a source file
src/emitterstate.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.i
.PHONY : src/emitterstate.cpp.i

src/emitterstate.s: src/emitterstate.cpp.s
.PHONY : src/emitterstate.s

# target to generate assembly for a file
src/emitterstate.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.s
.PHONY : src/emitterstate.cpp.s

src/emitterutils.o: src/emitterutils.cpp.o
.PHONY : src/emitterutils.o

# target to build an object file
src/emitterutils.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o
.PHONY : src/emitterutils.cpp.o

src/emitterutils.i: src/emitterutils.cpp.i
.PHONY : src/emitterutils.i

# target to preprocess a source file
src/emitterutils.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.i
.PHONY : src/emitterutils.cpp.i

src/emitterutils.s: src/emitterutils.cpp.s
.PHONY : src/emitterutils.s

# target to generate assembly for a file
src/emitterutils.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.s
.PHONY : src/emitterutils.cpp.s

src/exceptions.o: src/exceptions.cpp.o
.PHONY : src/exceptions.o

# target to build an object file
src/exceptions.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o
.PHONY : src/exceptions.cpp.o

src/exceptions.i: src/exceptions.cpp.i
.PHONY : src/exceptions.i

# target to preprocess a source file
src/exceptions.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.i
.PHONY : src/exceptions.cpp.i

src/exceptions.s: src/exceptions.cpp.s
.PHONY : src/exceptions.s

# target to generate assembly for a file
src/exceptions.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.s
.PHONY : src/exceptions.cpp.s

src/exp.o: src/exp.cpp.o
.PHONY : src/exp.o

# target to build an object file
src/exp.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.o
.PHONY : src/exp.cpp.o

src/exp.i: src/exp.cpp.i
.PHONY : src/exp.i

# target to preprocess a source file
src/exp.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.i
.PHONY : src/exp.cpp.i

src/exp.s: src/exp.cpp.s
.PHONY : src/exp.s

# target to generate assembly for a file
src/exp.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.s
.PHONY : src/exp.cpp.s

src/memory.o: src/memory.cpp.o
.PHONY : src/memory.o

# target to build an object file
src/memory.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.o
.PHONY : src/memory.cpp.o

src/memory.i: src/memory.cpp.i
.PHONY : src/memory.i

# target to preprocess a source file
src/memory.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.i
.PHONY : src/memory.cpp.i

src/memory.s: src/memory.cpp.s
.PHONY : src/memory.s

# target to generate assembly for a file
src/memory.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.s
.PHONY : src/memory.cpp.s

src/node.o: src/node.cpp.o
.PHONY : src/node.o

# target to build an object file
src/node.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.o
.PHONY : src/node.cpp.o

src/node.i: src/node.cpp.i
.PHONY : src/node.i

# target to preprocess a source file
src/node.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.i
.PHONY : src/node.cpp.i

src/node.s: src/node.cpp.s
.PHONY : src/node.s

# target to generate assembly for a file
src/node.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.s
.PHONY : src/node.cpp.s

src/node_data.o: src/node_data.cpp.o
.PHONY : src/node_data.o

# target to build an object file
src/node_data.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o
.PHONY : src/node_data.cpp.o

src/node_data.i: src/node_data.cpp.i
.PHONY : src/node_data.i

# target to preprocess a source file
src/node_data.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.i
.PHONY : src/node_data.cpp.i

src/node_data.s: src/node_data.cpp.s
.PHONY : src/node_data.s

# target to generate assembly for a file
src/node_data.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.s
.PHONY : src/node_data.cpp.s

src/nodebuilder.o: src/nodebuilder.cpp.o
.PHONY : src/nodebuilder.o

# target to build an object file
src/nodebuilder.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o
.PHONY : src/nodebuilder.cpp.o

src/nodebuilder.i: src/nodebuilder.cpp.i
.PHONY : src/nodebuilder.i

# target to preprocess a source file
src/nodebuilder.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.i
.PHONY : src/nodebuilder.cpp.i

src/nodebuilder.s: src/nodebuilder.cpp.s
.PHONY : src/nodebuilder.s

# target to generate assembly for a file
src/nodebuilder.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.s
.PHONY : src/nodebuilder.cpp.s

src/nodeevents.o: src/nodeevents.cpp.o
.PHONY : src/nodeevents.o

# target to build an object file
src/nodeevents.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o
.PHONY : src/nodeevents.cpp.o

src/nodeevents.i: src/nodeevents.cpp.i
.PHONY : src/nodeevents.i

# target to preprocess a source file
src/nodeevents.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.i
.PHONY : src/nodeevents.cpp.i

src/nodeevents.s: src/nodeevents.cpp.s
.PHONY : src/nodeevents.s

# target to generate assembly for a file
src/nodeevents.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.s
.PHONY : src/nodeevents.cpp.s

src/null.o: src/null.cpp.o
.PHONY : src/null.o

# target to build an object file
src/null.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.o
.PHONY : src/null.cpp.o

src/null.i: src/null.cpp.i
.PHONY : src/null.i

# target to preprocess a source file
src/null.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.i
.PHONY : src/null.cpp.i

src/null.s: src/null.cpp.s
.PHONY : src/null.s

# target to generate assembly for a file
src/null.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.s
.PHONY : src/null.cpp.s

src/ostream_wrapper.o: src/ostream_wrapper.cpp.o
.PHONY : src/ostream_wrapper.o

# target to build an object file
src/ostream_wrapper.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o
.PHONY : src/ostream_wrapper.cpp.o

src/ostream_wrapper.i: src/ostream_wrapper.cpp.i
.PHONY : src/ostream_wrapper.i

# target to preprocess a source file
src/ostream_wrapper.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.i
.PHONY : src/ostream_wrapper.cpp.i

src/ostream_wrapper.s: src/ostream_wrapper.cpp.s
.PHONY : src/ostream_wrapper.s

# target to generate assembly for a file
src/ostream_wrapper.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.s
.PHONY : src/ostream_wrapper.cpp.s

src/parse.o: src/parse.cpp.o
.PHONY : src/parse.o

# target to build an object file
src/parse.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.o
.PHONY : src/parse.cpp.o

src/parse.i: src/parse.cpp.i
.PHONY : src/parse.i

# target to preprocess a source file
src/parse.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.i
.PHONY : src/parse.cpp.i

src/parse.s: src/parse.cpp.s
.PHONY : src/parse.s

# target to generate assembly for a file
src/parse.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.s
.PHONY : src/parse.cpp.s

src/parser.o: src/parser.cpp.o
.PHONY : src/parser.o

# target to build an object file
src/parser.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.o
.PHONY : src/parser.cpp.o

src/parser.i: src/parser.cpp.i
.PHONY : src/parser.i

# target to preprocess a source file
src/parser.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.i
.PHONY : src/parser.cpp.i

src/parser.s: src/parser.cpp.s
.PHONY : src/parser.s

# target to generate assembly for a file
src/parser.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.s
.PHONY : src/parser.cpp.s

src/regex_yaml.o: src/regex_yaml.cpp.o
.PHONY : src/regex_yaml.o

# target to build an object file
src/regex_yaml.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o
.PHONY : src/regex_yaml.cpp.o

src/regex_yaml.i: src/regex_yaml.cpp.i
.PHONY : src/regex_yaml.i

# target to preprocess a source file
src/regex_yaml.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.i
.PHONY : src/regex_yaml.cpp.i

src/regex_yaml.s: src/regex_yaml.cpp.s
.PHONY : src/regex_yaml.s

# target to generate assembly for a file
src/regex_yaml.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.s
.PHONY : src/regex_yaml.cpp.s

src/scanner.o: src/scanner.cpp.o
.PHONY : src/scanner.o

# target to build an object file
src/scanner.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o
.PHONY : src/scanner.cpp.o

src/scanner.i: src/scanner.cpp.i
.PHONY : src/scanner.i

# target to preprocess a source file
src/scanner.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.i
.PHONY : src/scanner.cpp.i

src/scanner.s: src/scanner.cpp.s
.PHONY : src/scanner.s

# target to generate assembly for a file
src/scanner.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.s
.PHONY : src/scanner.cpp.s

src/scanscalar.o: src/scanscalar.cpp.o
.PHONY : src/scanscalar.o

# target to build an object file
src/scanscalar.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o
.PHONY : src/scanscalar.cpp.o

src/scanscalar.i: src/scanscalar.cpp.i
.PHONY : src/scanscalar.i

# target to preprocess a source file
src/scanscalar.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.i
.PHONY : src/scanscalar.cpp.i

src/scanscalar.s: src/scanscalar.cpp.s
.PHONY : src/scanscalar.s

# target to generate assembly for a file
src/scanscalar.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.s
.PHONY : src/scanscalar.cpp.s

src/scantag.o: src/scantag.cpp.o
.PHONY : src/scantag.o

# target to build an object file
src/scantag.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o
.PHONY : src/scantag.cpp.o

src/scantag.i: src/scantag.cpp.i
.PHONY : src/scantag.i

# target to preprocess a source file
src/scantag.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.i
.PHONY : src/scantag.cpp.i

src/scantag.s: src/scantag.cpp.s
.PHONY : src/scantag.s

# target to generate assembly for a file
src/scantag.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.s
.PHONY : src/scantag.cpp.s

src/scantoken.o: src/scantoken.cpp.o
.PHONY : src/scantoken.o

# target to build an object file
src/scantoken.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o
.PHONY : src/scantoken.cpp.o

src/scantoken.i: src/scantoken.cpp.i
.PHONY : src/scantoken.i

# target to preprocess a source file
src/scantoken.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.i
.PHONY : src/scantoken.cpp.i

src/scantoken.s: src/scantoken.cpp.s
.PHONY : src/scantoken.s

# target to generate assembly for a file
src/scantoken.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.s
.PHONY : src/scantoken.cpp.s

src/simplekey.o: src/simplekey.cpp.o
.PHONY : src/simplekey.o

# target to build an object file
src/simplekey.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o
.PHONY : src/simplekey.cpp.o

src/simplekey.i: src/simplekey.cpp.i
.PHONY : src/simplekey.i

# target to preprocess a source file
src/simplekey.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.i
.PHONY : src/simplekey.cpp.i

src/simplekey.s: src/simplekey.cpp.s
.PHONY : src/simplekey.s

# target to generate assembly for a file
src/simplekey.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.s
.PHONY : src/simplekey.cpp.s

src/singledocparser.o: src/singledocparser.cpp.o
.PHONY : src/singledocparser.o

# target to build an object file
src/singledocparser.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o
.PHONY : src/singledocparser.cpp.o

src/singledocparser.i: src/singledocparser.cpp.i
.PHONY : src/singledocparser.i

# target to preprocess a source file
src/singledocparser.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.i
.PHONY : src/singledocparser.cpp.i

src/singledocparser.s: src/singledocparser.cpp.s
.PHONY : src/singledocparser.s

# target to generate assembly for a file
src/singledocparser.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.s
.PHONY : src/singledocparser.cpp.s

src/stream.o: src/stream.cpp.o
.PHONY : src/stream.o

# target to build an object file
src/stream.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.o
.PHONY : src/stream.cpp.o

src/stream.i: src/stream.cpp.i
.PHONY : src/stream.i

# target to preprocess a source file
src/stream.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.i
.PHONY : src/stream.cpp.i

src/stream.s: src/stream.cpp.s
.PHONY : src/stream.s

# target to generate assembly for a file
src/stream.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.s
.PHONY : src/stream.cpp.s

src/tag.o: src/tag.cpp.o
.PHONY : src/tag.o

# target to build an object file
src/tag.cpp.o:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.o
.PHONY : src/tag.cpp.o

src/tag.i: src/tag.cpp.i
.PHONY : src/tag.i

# target to preprocess a source file
src/tag.cpp.i:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.i
.PHONY : src/tag.cpp.i

src/tag.s: src/tag.cpp.s
.PHONY : src/tag.s

# target to generate assembly for a file
src/tag.cpp.s:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/yaml-cpp.dir/build.make lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.s
.PHONY : src/tag.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... Continuous"
	@echo "... ContinuousBuild"
	@echo "... ContinuousConfigure"
	@echo "... ContinuousCoverage"
	@echo "... ContinuousMemCheck"
	@echo "... ContinuousStart"
	@echo "... ContinuousSubmit"
	@echo "... ContinuousTest"
	@echo "... ContinuousUpdate"
	@echo "... Experimental"
	@echo "... ExperimentalBuild"
	@echo "... ExperimentalConfigure"
	@echo "... ExperimentalCoverage"
	@echo "... ExperimentalMemCheck"
	@echo "... ExperimentalStart"
	@echo "... ExperimentalSubmit"
	@echo "... ExperimentalTest"
	@echo "... ExperimentalUpdate"
	@echo "... Nightly"
	@echo "... NightlyBuild"
	@echo "... NightlyConfigure"
	@echo "... NightlyCoverage"
	@echo "... NightlyMemCheck"
	@echo "... NightlyMemoryCheck"
	@echo "... NightlyStart"
	@echo "... NightlySubmit"
	@echo "... NightlyTest"
	@echo "... NightlyUpdate"
	@echo "... yaml-cpp"
	@echo "... src/binary.o"
	@echo "... src/binary.i"
	@echo "... src/binary.s"
	@echo "... src/contrib/graphbuilder.o"
	@echo "... src/contrib/graphbuilder.i"
	@echo "... src/contrib/graphbuilder.s"
	@echo "... src/contrib/graphbuilderadapter.o"
	@echo "... src/contrib/graphbuilderadapter.i"
	@echo "... src/contrib/graphbuilderadapter.s"
	@echo "... src/convert.o"
	@echo "... src/convert.i"
	@echo "... src/convert.s"
	@echo "... src/depthguard.o"
	@echo "... src/depthguard.i"
	@echo "... src/depthguard.s"
	@echo "... src/directives.o"
	@echo "... src/directives.i"
	@echo "... src/directives.s"
	@echo "... src/emit.o"
	@echo "... src/emit.i"
	@echo "... src/emit.s"
	@echo "... src/emitfromevents.o"
	@echo "... src/emitfromevents.i"
	@echo "... src/emitfromevents.s"
	@echo "... src/emitter.o"
	@echo "... src/emitter.i"
	@echo "... src/emitter.s"
	@echo "... src/emitterstate.o"
	@echo "... src/emitterstate.i"
	@echo "... src/emitterstate.s"
	@echo "... src/emitterutils.o"
	@echo "... src/emitterutils.i"
	@echo "... src/emitterutils.s"
	@echo "... src/exceptions.o"
	@echo "... src/exceptions.i"
	@echo "... src/exceptions.s"
	@echo "... src/exp.o"
	@echo "... src/exp.i"
	@echo "... src/exp.s"
	@echo "... src/memory.o"
	@echo "... src/memory.i"
	@echo "... src/memory.s"
	@echo "... src/node.o"
	@echo "... src/node.i"
	@echo "... src/node.s"
	@echo "... src/node_data.o"
	@echo "... src/node_data.i"
	@echo "... src/node_data.s"
	@echo "... src/nodebuilder.o"
	@echo "... src/nodebuilder.i"
	@echo "... src/nodebuilder.s"
	@echo "... src/nodeevents.o"
	@echo "... src/nodeevents.i"
	@echo "... src/nodeevents.s"
	@echo "... src/null.o"
	@echo "... src/null.i"
	@echo "... src/null.s"
	@echo "... src/ostream_wrapper.o"
	@echo "... src/ostream_wrapper.i"
	@echo "... src/ostream_wrapper.s"
	@echo "... src/parse.o"
	@echo "... src/parse.i"
	@echo "... src/parse.s"
	@echo "... src/parser.o"
	@echo "... src/parser.i"
	@echo "... src/parser.s"
	@echo "... src/regex_yaml.o"
	@echo "... src/regex_yaml.i"
	@echo "... src/regex_yaml.s"
	@echo "... src/scanner.o"
	@echo "... src/scanner.i"
	@echo "... src/scanner.s"
	@echo "... src/scanscalar.o"
	@echo "... src/scanscalar.i"
	@echo "... src/scanscalar.s"
	@echo "... src/scantag.o"
	@echo "... src/scantag.i"
	@echo "... src/scantag.s"
	@echo "... src/scantoken.o"
	@echo "... src/scantoken.i"
	@echo "... src/scantoken.s"
	@echo "... src/simplekey.o"
	@echo "... src/simplekey.i"
	@echo "... src/simplekey.s"
	@echo "... src/singledocparser.o"
	@echo "... src/singledocparser.i"
	@echo "... src/singledocparser.s"
	@echo "... src/stream.o"
	@echo "... src/stream.i"
	@echo "... src/stream.s"
	@echo "... src/tag.o"
	@echo "... src/tag.i"
	@echo "... src/tag.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -P /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles/VerifyGlobs.cmake
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

