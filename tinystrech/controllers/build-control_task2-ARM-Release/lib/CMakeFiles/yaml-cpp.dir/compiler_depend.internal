# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/binary.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/binary.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h

lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilder.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilderadapter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/contrib/anchordict.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/contrib/graphbuilder.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/eventhandler.h
 /usr/local/include/yaml-cpp/parser.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h

lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilderadapter.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilderadapter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/contrib/anchordict.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/contrib/graphbuilder.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/eventhandler.h

lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/convert.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/algorithm
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algo.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/algorithmfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uniform_int_dist.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /usr/local/include/yaml-cpp/node/convert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cmath
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/math.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/math-vector.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libm-simd-decl-stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/flt-eval-method.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/fp-logb.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/fp-fast.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls-helper-functions.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls-narrow.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/iscanonical.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathinline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_list.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/list.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/binary.h
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/node/impl.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/noexcept.h
 /usr/local/include/yaml-cpp/traits.h
 /usr/local/include/yaml-cpp/node/detail/memory.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /usr/local/include/yaml-cpp/node/ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/node/detail/node.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/node/detail/node_ref.h
 /usr/local/include/yaml-cpp/node/type.h
 /usr/local/include/yaml-cpp/node/detail/node_data.h
 /usr/local/include/yaml-cpp/node/detail/node_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iterator
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stream_iterator.h
 /usr/local/include/yaml-cpp/node/iterator.h
 /usr/local/include/yaml-cpp/node/node.h
 /usr/local/include/yaml-cpp/node/detail/iterator_fwd.h
 /usr/local/include/yaml-cpp/node/detail/iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/atomic
 /usr/local/include/yaml-cpp/null.h

lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/depthguard.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/depthguard.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/noexcept.h
 /usr/local/include/yaml-cpp/traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc

lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h

lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emit.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/node/emit.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/node/ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/emitfromevents.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/eventhandler.h
 /usr/local/include/yaml-cpp/emitter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cmath
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/math.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/math-vector.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libm-simd-decl-stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/flt-eval-method.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/fp-logb.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/fp-fast.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls-helper-functions.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls-narrow.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/iscanonical.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathinline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /usr/local/include/yaml-cpp/binary.h
 /usr/local/include/yaml-cpp/emitterdef.h
 /usr/local/include/yaml-cpp/emittermanip.h
 /usr/local/include/yaml-cpp/null.h
 /usr/local/include/yaml-cpp/ostream_wrapper.h

lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitfromevents.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /usr/local/include/yaml-cpp/emitfromevents.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/eventhandler.h
 /usr/local/include/yaml-cpp/emitter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cmath
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/math.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/math-vector.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libm-simd-decl-stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/flt-eval-method.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/fp-logb.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/fp-fast.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls-helper-functions.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls-narrow.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/iscanonical.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathinline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/binary.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/emitterdef.h
 /usr/local/include/yaml-cpp/emittermanip.h
 /usr/local/include/yaml-cpp/null.h
 /usr/local/include/yaml-cpp/ostream_wrapper.h

lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitter.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterutils.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/setting.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/emitterdef.h
 /usr/local/include/yaml-cpp/emittermanip.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /usr/local/include/yaml-cpp/ostream_wrapper.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/indentation.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/emitter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cmath
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/math.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/math-vector.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libm-simd-decl-stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/flt-eval-method.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/fp-logb.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/fp-fast.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls-helper-functions.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathcalls-narrow.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/iscanonical.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/mathinline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /usr/local/include/yaml-cpp/binary.h
 /usr/local/include/yaml-cpp/null.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/traits.h

lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/setting.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/emitterdef.h
 /usr/local/include/yaml-cpp/emittermanip.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc

lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterutils.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/algorithm
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algo.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/algorithmfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uniform_int_dist.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iomanip
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/locale
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets_nonio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ctime
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/time_members.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/messages_members.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/libintl.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/codecvt.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets_nonio.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_conv.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterutils.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/setting.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/emitterdef.h
 /usr/local/include/yaml-cpp/emittermanip.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /usr/local/include/yaml-cpp/ostream_wrapper.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regeximpl.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/streamcharsource.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stringsource.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/indentation.h
 /usr/local/include/yaml-cpp/binary.h
 /usr/local/include/yaml-cpp/null.h

lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exceptions.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/noexcept.h
 /usr/local/include/yaml-cpp/traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc

lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regeximpl.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/streamcharsource.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stringsource.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h

lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/memory.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/node/detail/memory.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/node/ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/node/detail/node.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/node/detail/node_ref.h
 /usr/local/include/yaml-cpp/node/type.h
 /usr/local/include/yaml-cpp/node/detail/node_data.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_list.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/list.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/node/detail/node_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iterator
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stream_iterator.h
 /usr/local/include/yaml-cpp/node/iterator.h
 /usr/local/include/yaml-cpp/node/node.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/node/detail/iterator_fwd.h
 /usr/local/include/yaml-cpp/node/detail/iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/atomic

lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/node/node.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/node/detail/iterator_fwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_list.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/list.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/node/ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/node/type.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.h
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/eventhandler.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h

lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node_data.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/algorithm
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algo.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/algorithmfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uniform_int_dist.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iterator
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stream_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /usr/local/include/yaml-cpp/noexcept.h
 /usr/local/include/yaml-cpp/traits.h
 /usr/local/include/yaml-cpp/node/detail/memory.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /usr/local/include/yaml-cpp/node/ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/node/detail/node.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/node/detail/node_ref.h
 /usr/local/include/yaml-cpp/node/type.h
 /usr/local/include/yaml-cpp/node/detail/node_data.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_list.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/list.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/node/detail/node_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/node/iterator.h
 /usr/local/include/yaml-cpp/node/node.h
 /usr/local/include/yaml-cpp/node/detail/iterator_fwd.h
 /usr/local/include/yaml-cpp/node/detail/iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/atomic

lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/eventhandler.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /usr/local/include/yaml-cpp/node/ptr.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/node/detail/node.h
 /usr/local/include/yaml-cpp/node/detail/node_ref.h
 /usr/local/include/yaml-cpp/node/type.h
 /usr/local/include/yaml-cpp/node/detail/node_data.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_list.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/list.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /usr/local/include/yaml-cpp/node/detail/node_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iterator
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stream_iterator.h
 /usr/local/include/yaml-cpp/node/iterator.h
 /usr/local/include/yaml-cpp/node/node.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/node/detail/iterator_fwd.h
 /usr/local/include/yaml-cpp/node/detail/iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/atomic
 /usr/local/include/yaml-cpp/node/impl.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/noexcept.h
 /usr/local/include/yaml-cpp/traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /usr/local/include/yaml-cpp/node/detail/memory.h

lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/node/ptr.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/eventhandler.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/node/detail/node.h
 /usr/local/include/yaml-cpp/node/detail/node_ref.h
 /usr/local/include/yaml-cpp/node/type.h
 /usr/local/include/yaml-cpp/node/detail/node_data.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_list.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/list.tcc
 /usr/local/include/yaml-cpp/node/detail/node_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iterator
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stream_iterator.h
 /usr/local/include/yaml-cpp/node/iterator.h
 /usr/local/include/yaml-cpp/node/node.h
 /usr/local/include/yaml-cpp/node/detail/iterator_fwd.h
 /usr/local/include/yaml-cpp/node/detail/iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/atomic

lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/null.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/null.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc

lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ostream_wrapper.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/ostream_wrapper.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/algorithm
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algo.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/algorithmfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uniform_int_dist.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstring
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/string.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/strings.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc

lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parse.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /usr/local/include/yaml-cpp/node/parse.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/fstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/codecvt.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/basic_file.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++io.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/fstream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.h
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/eventhandler.h
 /usr/local/include/yaml-cpp/node/ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /usr/local/include/yaml-cpp/node/impl.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/noexcept.h
 /usr/local/include/yaml-cpp/traits.h
 /usr/local/include/yaml-cpp/node/detail/memory.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /usr/local/include/yaml-cpp/node/detail/node.h
 /usr/local/include/yaml-cpp/node/detail/node_ref.h
 /usr/local/include/yaml-cpp/node/type.h
 /usr/local/include/yaml-cpp/node/detail/node_data.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_list.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/list.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /usr/local/include/yaml-cpp/node/detail/node_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iterator
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stream_iterator.h
 /usr/local/include/yaml-cpp/node/iterator.h
 /usr/local/include/yaml-cpp/node/node.h
 /usr/local/include/yaml-cpp/node/detail/iterator_fwd.h
 /usr/local/include/yaml-cpp/node/detail/iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/atomic
 /usr/local/include/yaml-cpp/parser.h

lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parser.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/queue
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_queue.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ptr_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/token.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/singledocparser.h
 /usr/local/include/yaml-cpp/anchor.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/noexcept.h
 /usr/local/include/yaml-cpp/traits.h
 /usr/local/include/yaml-cpp/parser.h

lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regeximpl.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/streamcharsource.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stringsource.h

lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regeximpl.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/streamcharsource.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stringsource.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/queue
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_queue.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ptr_vector.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/token.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc

lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanscalar.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanscalar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regeximpl.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/streamcharsource.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stringsource.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/algorithm
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algo.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/algorithmfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uniform_int_dist.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc

lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantag.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regeximpl.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/streamcharsource.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stringsource.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc

lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantoken.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regeximpl.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/streamcharsource.h
 /usr/local/include/yaml-cpp/noexcept.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stringsource.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/queue
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_queue.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ptr_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/token.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanscalar.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantag.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/traits.h

lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/simplekey.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/queue
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_queue.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ptr_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/token.h

lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/singledocparser.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/algorithm
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algo.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/algorithmfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_heap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tempbuf.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uniform_int_dist.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/limits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/sstream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/sstream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/collectionstack.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stack
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_stack.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/queue
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_queue.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ptr_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/memory
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_raw_storage_iter.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/concurrence.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/unique_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocated_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/refwrap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/shared_ptr_atomic.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/atomic_lockfree_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/auto_ptr.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/token.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/singledocparser.h
 /usr/local/include/yaml-cpp/anchor.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.h
 /usr/local/include/yaml-cpp/depthguard.h
 /usr/local/include/yaml-cpp/exceptions.h
 /usr/local/include/yaml-cpp/noexcept.h
 /usr/local/include/yaml-cpp/traits.h
 /usr/local/include/yaml-cpp/emitterstyle.h
 /usr/local/include/yaml-cpp/eventhandler.h
 /usr/local/include/yaml-cpp/null.h

lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstddef
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/deque
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_deque.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/deque.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/set
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_set.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multiset.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h

lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.o
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.cpp
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdc-predef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cassert
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++config.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/os_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/features.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/cdefs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wordsize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/long-double.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/gnu/stubs-lp64.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/cpu_defines.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/assert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/stdexcept
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/exception
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_ptr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/exception_defines.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_init_exception.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stddef.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/typeinfo
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/hash_bytes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/new
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/nested_exception.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/move.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/concept_check.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/type_traits
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/string
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stringfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/memoryfwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/char_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_algobase.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functexcept.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cpp_type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/type_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/numeric_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_pair.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_types.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator_base_funcs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/assertions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ptr_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/debug/debug.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/predefined_ops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/postypes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwchar
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/libc-header-start.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/floatn-common.h
 /home/<USER>/Desktop/cross-gcc/lib/gcc/aarch64-linux-gnu/9.3.0/include/stdarg.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wchar.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/wint_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__mbstate_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/locale_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__locale_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdint
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdint.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timesize.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/typesizes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time64.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-intn.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdint-uintn.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/new_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/localefwd.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/c++locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/clocale
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/locale.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/locale.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iosfwd
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/ctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/endianness.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream_insert.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/cxxabi_forced.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_function.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/backward/binders.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/range_access.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/initializer_list
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/atomicity.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/gthr-default.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/pthread.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/time_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timespec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sched.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_sched_param.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/cpu-set.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/time.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/timex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_timeval.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clock_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_tm.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/clockid_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/timer_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_itimerspec.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/thread-shared-types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/pthreadtypes-arch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_mutex.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/struct_rwlock.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/setjmp.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/atomic_word.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/alloc_traits.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/string_conversions.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdlib
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/stdlib.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitflags.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/waitstatus.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/types.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/endian.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/byteswap.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/uintn-identity.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/sys/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/select.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__sigset_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/alloca.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-bsearch.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdlib-float.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/std_abs.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cstdio
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/stdio.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/__fpos64_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/struct_FILE.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/cookie_io_functions_t.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio_lim.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/sys_errlist.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/stdio.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cerrno
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/linux/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/asm-generic/errno-base.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/types/error_t.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/functional_hash.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_string.tcc
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/map
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_tree.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ext/aligned_buffer.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_map.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/tuple
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/utility
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_relops.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/array
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/uses_allocator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/invoke.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_multimap.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/erase_if.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.h
 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/token.h
 /usr/local/include/yaml-cpp/mark.h
 /usr/local/include/yaml-cpp/dll.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/iostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ostream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/ios
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ios_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_classes.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/system_error
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/error_constants.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/streambuf
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/cwctype
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/wctype.h
 /home/<USER>/Downloads/cross-gcc/aarch64-linux-gnu/include/bits/wctype-wchar.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_base.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/streambuf_iterator.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/aarch64-linux-gnu/bits/ctype_inline.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/locale_facets.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/basic_ios.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/ostream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/istream
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/istream.tcc
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/vector
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_construct.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_uninitialized.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_vector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/stl_bvector.h
 /home/<USER>/Desktop/cross-gcc/aarch64-linux-gnu/include/c++/9.3.0/bits/vector.tcc

