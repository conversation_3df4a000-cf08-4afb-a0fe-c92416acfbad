# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/tinystrech/controllers/control_task2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release

# Include any dependencies generated for this target.
include lib/CMakeFiles/yaml-cpp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include lib/CMakeFiles/yaml-cpp.dir/compiler_depend.make

# Include the progress variables for this target.
include lib/CMakeFiles/yaml-cpp.dir/progress.make

# Include the compile flags for this target's objects.
include lib/CMakeFiles/yaml-cpp.dir/flags.make

lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilder.cpp
lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilder.cpp

lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilder.cpp > CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilder.cpp -o CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilderadapter.cpp
lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilderadapter.cpp

lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilderadapter.cpp > CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/contrib/graphbuilderadapter.cpp -o CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/binary.cpp
lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/binary.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/binary.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/binary.cpp

lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/binary.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/binary.cpp > CMakeFiles/yaml-cpp.dir/src/binary.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/binary.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/binary.cpp -o CMakeFiles/yaml-cpp.dir/src/binary.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/convert.cpp
lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/convert.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/convert.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/convert.cpp

lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/convert.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/convert.cpp > CMakeFiles/yaml-cpp.dir/src/convert.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/convert.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/convert.cpp -o CMakeFiles/yaml-cpp.dir/src/convert.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/depthguard.cpp
lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/depthguard.cpp

lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/depthguard.cpp > CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/depthguard.cpp -o CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.cpp
lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/directives.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/directives.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.cpp

lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/directives.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.cpp > CMakeFiles/yaml-cpp.dir/src/directives.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/directives.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/directives.cpp -o CMakeFiles/yaml-cpp.dir/src/directives.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emit.cpp
lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/emit.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/emit.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emit.cpp

lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/emit.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emit.cpp > CMakeFiles/yaml-cpp.dir/src/emit.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/emit.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emit.cpp -o CMakeFiles/yaml-cpp.dir/src/emit.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitfromevents.cpp
lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitfromevents.cpp

lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitfromevents.cpp > CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitfromevents.cpp -o CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitter.cpp
lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitter.cpp

lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/emitter.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitter.cpp > CMakeFiles/yaml-cpp.dir/src/emitter.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/emitter.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitter.cpp -o CMakeFiles/yaml-cpp.dir/src/emitter.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.cpp
lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.cpp

lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.cpp > CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterstate.cpp -o CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterutils.cpp
lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterutils.cpp

lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterutils.cpp > CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/emitterutils.cpp -o CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exceptions.cpp
lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exceptions.cpp

lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exceptions.cpp > CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exceptions.cpp -o CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.cpp
lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/exp.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/exp.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.cpp

lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/exp.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.cpp > CMakeFiles/yaml-cpp.dir/src/exp.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/exp.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/exp.cpp -o CMakeFiles/yaml-cpp.dir/src/exp.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/memory.cpp
lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/memory.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/memory.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/memory.cpp

lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/memory.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/memory.cpp > CMakeFiles/yaml-cpp.dir/src/memory.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/memory.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/memory.cpp -o CMakeFiles/yaml-cpp.dir/src/memory.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node.cpp
lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/node.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/node.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node.cpp

lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/node.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node.cpp > CMakeFiles/yaml-cpp.dir/src/node.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/node.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node.cpp -o CMakeFiles/yaml-cpp.dir/src/node.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node_data.cpp
lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node_data.cpp

lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/node_data.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node_data.cpp > CMakeFiles/yaml-cpp.dir/src/node_data.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/node_data.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/node_data.cpp -o CMakeFiles/yaml-cpp.dir/src/node_data.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.cpp
lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.cpp

lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.cpp > CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodebuilder.cpp -o CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.cpp
lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.cpp

lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.cpp > CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/nodeevents.cpp -o CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/null.cpp
lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/null.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/null.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/null.cpp

lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/null.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/null.cpp > CMakeFiles/yaml-cpp.dir/src/null.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/null.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/null.cpp -o CMakeFiles/yaml-cpp.dir/src/null.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ostream_wrapper.cpp
lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ostream_wrapper.cpp

lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ostream_wrapper.cpp > CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/ostream_wrapper.cpp -o CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parse.cpp
lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/parse.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/parse.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parse.cpp

lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/parse.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parse.cpp > CMakeFiles/yaml-cpp.dir/src/parse.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/parse.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parse.cpp -o CMakeFiles/yaml-cpp.dir/src/parse.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parser.cpp
lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/parser.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/parser.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parser.cpp

lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/parser.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parser.cpp > CMakeFiles/yaml-cpp.dir/src/parser.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/parser.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/parser.cpp -o CMakeFiles/yaml-cpp.dir/src/parser.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.cpp
lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.cpp

lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.cpp > CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/regex_yaml.cpp -o CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.cpp
lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.cpp

lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/scanner.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.cpp > CMakeFiles/yaml-cpp.dir/src/scanner.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/scanner.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanner.cpp -o CMakeFiles/yaml-cpp.dir/src/scanner.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanscalar.cpp
lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanscalar.cpp

lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanscalar.cpp > CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scanscalar.cpp -o CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantag.cpp
lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantag.cpp

lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/scantag.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantag.cpp > CMakeFiles/yaml-cpp.dir/src/scantag.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/scantag.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantag.cpp -o CMakeFiles/yaml-cpp.dir/src/scantag.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantoken.cpp
lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantoken.cpp

lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantoken.cpp > CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/scantoken.cpp -o CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/simplekey.cpp
lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/simplekey.cpp

lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/simplekey.cpp > CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/simplekey.cpp -o CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/singledocparser.cpp
lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/singledocparser.cpp

lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/singledocparser.cpp > CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/singledocparser.cpp -o CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.cpp
lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/stream.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/stream.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.cpp

lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/stream.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.cpp > CMakeFiles/yaml-cpp.dir/src/stream.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/stream.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/stream.cpp -o CMakeFiles/yaml-cpp.dir/src/stream.cpp.s

lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.o: lib/CMakeFiles/yaml-cpp.dir/flags.make
lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.o: /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.cpp
lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.o: lib/CMakeFiles/yaml-cpp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.o"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.o -MF CMakeFiles/yaml-cpp.dir/src/tag.cpp.o.d -o CMakeFiles/yaml-cpp.dir/src/tag.cpp.o -c /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.cpp

lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/yaml-cpp.dir/src/tag.cpp.i"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.cpp > CMakeFiles/yaml-cpp.dir/src/tag.cpp.i

lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/yaml-cpp.dir/src/tag.cpp.s"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cross-gcc/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp/src/tag.cpp -o CMakeFiles/yaml-cpp.dir/src/tag.cpp.s

# Object files for target yaml-cpp
yaml__cpp_OBJECTS = \
"CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/binary.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/convert.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/directives.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/emit.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/exp.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/memory.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/node.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/null.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/parse.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/parser.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/stream.cpp.o" \
"CMakeFiles/yaml-cpp.dir/src/tag.cpp.o"

# External object files for target yaml-cpp
yaml__cpp_EXTERNAL_OBJECTS =

lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilder.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/contrib/graphbuilderadapter.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/binary.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/convert.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/depthguard.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/directives.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/emit.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/emitfromevents.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/emitter.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/emitterstate.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/emitterutils.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/exceptions.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/exp.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/memory.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/node.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/node_data.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/nodebuilder.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/nodeevents.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/null.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/ostream_wrapper.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/parse.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/parser.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/regex_yaml.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/scanner.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/scanscalar.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/scantag.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/scantoken.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/simplekey.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/singledocparser.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/stream.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/src/tag.cpp.o
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/build.make
lib/libyaml-cpp.a: lib/CMakeFiles/yaml-cpp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Linking CXX static library libyaml-cpp.a"
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && $(CMAKE_COMMAND) -P CMakeFiles/yaml-cpp.dir/cmake_clean_target.cmake
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/yaml-cpp.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
lib/CMakeFiles/yaml-cpp.dir/build: lib/libyaml-cpp.a
.PHONY : lib/CMakeFiles/yaml-cpp.dir/build

lib/CMakeFiles/yaml-cpp.dir/clean:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && $(CMAKE_COMMAND) -P CMakeFiles/yaml-cpp.dir/cmake_clean.cmake
.PHONY : lib/CMakeFiles/yaml-cpp.dir/clean

lib/CMakeFiles/yaml-cpp.dir/depend:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/tinystrech/controllers/control_task2 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib/CMakeFiles/yaml-cpp.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : lib/CMakeFiles/yaml-cpp.dir/depend

