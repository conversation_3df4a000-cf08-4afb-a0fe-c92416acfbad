# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/tinystrech/controllers/control_task2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release

# Utility rule file for NightlyStart.

# Include any custom commands dependencies for this target.
include lib/CMakeFiles/NightlyStart.dir/compiler_depend.make

# Include the progress variables for this target.
include lib/CMakeFiles/NightlyStart.dir/progress.make

lib/CMakeFiles/NightlyStart:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/ctest -D NightlyStart

NightlyStart: lib/CMakeFiles/NightlyStart
NightlyStart: lib/CMakeFiles/NightlyStart.dir/build.make
.PHONY : NightlyStart

# Rule to build all files generated by this target.
lib/CMakeFiles/NightlyStart.dir/build: NightlyStart
.PHONY : lib/CMakeFiles/NightlyStart.dir/build

lib/CMakeFiles/NightlyStart.dir/clean:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && $(CMAKE_COMMAND) -P CMakeFiles/NightlyStart.dir/cmake_clean.cmake
.PHONY : lib/CMakeFiles/NightlyStart.dir/clean

lib/CMakeFiles/NightlyStart.dir/depend:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/tinystrech/controllers/control_task2 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib/CMakeFiles/NightlyStart.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : lib/CMakeFiles/NightlyStart.dir/depend

