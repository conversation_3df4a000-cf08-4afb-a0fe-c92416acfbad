# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.20

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake

# The command to remove a file.
RM = /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Documents/tinystrech/controllers/control_task2

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release

# Utility rule file for ExperimentalSubmit.

# Include any custom commands dependencies for this target.
include lib/CMakeFiles/ExperimentalSubmit.dir/compiler_depend.make

# Include the progress variables for this target.
include lib/CMakeFiles/ExperimentalSubmit.dir/progress.make

lib/CMakeFiles/ExperimentalSubmit:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && /home/<USER>/Desktop/cmake-3.20.5-linux-x86_64/bin/ctest -D ExperimentalSubmit

ExperimentalSubmit: lib/CMakeFiles/ExperimentalSubmit
ExperimentalSubmit: lib/CMakeFiles/ExperimentalSubmit.dir/build.make
.PHONY : ExperimentalSubmit

# Rule to build all files generated by this target.
lib/CMakeFiles/ExperimentalSubmit.dir/build: ExperimentalSubmit
.PHONY : lib/CMakeFiles/ExperimentalSubmit.dir/build

lib/CMakeFiles/ExperimentalSubmit.dir/clean:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib && $(CMAKE_COMMAND) -P CMakeFiles/ExperimentalSubmit.dir/cmake_clean.cmake
.PHONY : lib/CMakeFiles/ExperimentalSubmit.dir/clean

lib/CMakeFiles/ExperimentalSubmit.dir/depend:
	cd /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/Documents/tinystrech/controllers/control_task2 /home/<USER>/Documents/tinystrech/controllers/tinystretch/yaml-cpp /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib /home/<USER>/Documents/tinystrech/controllers/build-control_task2-ARM-Release/lib/CMakeFiles/ExperimentalSubmit.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : lib/CMakeFiles/ExperimentalSubmit.dir/depend

