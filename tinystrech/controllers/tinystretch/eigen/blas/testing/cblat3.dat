'cblat3.summ'     NAME OF <PERSON>UMMARY OUTPUT FILE
6                 UNIT NUMBER OF SUMMARY FILE
'cblat3.snap'     NAME OF SNAPSHOT OUTPUT FILE
-1                UNIT NUMBER OF SNAPSHOT FILE (NOT USED IF .LT. 0)
F        LOGICAL FLAG, T TO REWIND SNAPSHOT FILE AFTER EACH RECORD.
F        LOGICAL FLAG, T TO STOP ON FAILURES.
F        LOGICAL FLAG, T TO TEST ERROR EXITS.
16.0     THRESHOLD VALUE OF TEST RATIO
6                 NUMBER OF VALUES OF N
0 1 2 3 5 9       VALUES OF N
3                 NUMBER OF VALUES OF ALPHA
(0.0,0.0) (1.0,0.0) (0.7,-0.9)       VALUES OF ALPHA
3                 NUMBER OF VALUES OF BETA
(0.0,0.0) (1.0,0.0) (1.3,-1.1)       VALUES OF BETA
CGEMM  T PUT F FOR NO TEST. SAME COLUMNS.
CHEMM  T PUT F FOR NO TEST. SAME COLUMNS.
CSYMM  T PUT F FOR NO TEST. SAME COLUMNS.
CTRMM  T PUT F FOR NO TEST. SAME COLUMNS.
CTRSM  T PUT F FOR NO TEST. SAME COLUMNS.
CHERK  T PUT F FOR NO TEST. SAME COLUMNS.
CSYRK  T PUT F FOR NO TEST. SAME COLUMNS.
CHER2K T PUT F FOR NO TEST. SAME COLUMNS.
CSYR2K T PUT F FOR NO TEST. SAME COLUMNS.
