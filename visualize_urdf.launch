<?xml version="1.0"?>
<launch>
  <!-- Load the URDF into the ROS Parameter Server -->
  <param name="robot_description" 
         command="$(find xacro)/xacro --inorder $(find TinyStretch)/urdf/TinyStretch_arm.urdf" />
  
  <!-- Send fake joint values -->
  <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
    <param name="use_gui" value="true"/>
  </node>
  
  <!-- Combine joint values -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"/>
  
  <!-- Show in Rviz -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(find urdf_tutorial)/rviz/urdf.rviz"/>
</launch>
