#!/usr/bin/env python3
"""
URDF可视化脚本
提供多种方式来可视化URDF文件
"""

import os
import subprocess
import sys

def check_dependencies():
    """检查必要的依赖"""
    dependencies = {
        'urdf_parser_py': 'pip install urdf-parser-py',
        'pybullet': 'pip install pybullet',
        'matplotlib': 'pip install matplotlib'
    }
    
    missing = []
    for dep, install_cmd in dependencies.items():
        try:
            __import__(dep.replace('-', '_'))
        except ImportError:
            missing.append((dep, install_cmd))
    
    if missing:
        print("缺少以下依赖:")
        for dep, cmd in missing:
            print(f"  {dep}: {cmd}")
        return False
    return True

def visualize_with_pybullet(urdf_path):
    """使用PyBullet可视化URDF"""
    try:
        import pybullet as p
        import pybullet_data
        import time
        
        # 启动PyBullet GUI
        physicsClient = p.connect(p.GUI)
        p.setAdditionalSearchPath(pybullet_data.getDataPath())
        
        # 设置重力
        p.setGravity(0, 0, -10)
        
        # 加载地面
        planeId = p.loadURDF("plane.urdf")
        
        # 加载URDF文件
        startPos = [0, 0, 1]
        startOrientation = p.getQuaternionFromEuler([0, 0, 0])
        robotId = p.loadURDF(urdf_path, startPos, startOrientation)
        
        print("PyBullet可视化已启动!")
        print("按Ctrl+C退出")
        
        # 保持可视化窗口打开
        try:
            while True:
                p.stepSimulation()
                time.sleep(1./240.)
        except KeyboardInterrupt:
            print("退出可视化")
        
        p.disconnect()
        
    except ImportError:
        print("PyBullet未安装，请运行: pip install pybullet")
    except Exception as e:
        print(f"PyBullet可视化失败: {e}")

def generate_urdf_graph(urdf_path):
    """生成URDF的图形表示"""
    try:
        # 使用urdf_to_graphiz生成图形
        output_file = "urdf_graph.pdf"
        cmd = f"urdf_to_graphiz {urdf_path}"
        
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"URDF图形已生成: {output_file}")
        else:
            print("urdf_to_graphiz未找到，尝试使用ROS工具")
            # 尝试使用ROS工具
            cmd = f"rosrun urdf_parser urdf_to_graphiz {urdf_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"URDF图形已生成: {output_file}")
            else:
                print("无法生成URDF图形，请确保安装了相关工具")
                
    except Exception as e:
        print(f"生成图形失败: {e}")

def print_urdf_info(urdf_path):
    """打印URDF文件信息"""
    try:
        from urdf_parser_py.urdf import URDF
        
        robot = URDF.from_xml_file(urdf_path)
        
        print(f"\n=== URDF信息 ===")
        print(f"机器人名称: {robot.name}")
        print(f"连杆数量: {len(robot.links)}")
        print(f"关节数量: {len(robot.joints)}")
        
        print(f"\n=== 连杆列表 ===")
        for link in robot.links:
            print(f"  - {link.name}")
            
        print(f"\n=== 关节列表 ===")
        for joint in robot.joints:
            print(f"  - {joint.name} ({joint.type}): {joint.parent} -> {joint.child}")
            
    except ImportError:
        print("urdf_parser_py未安装，请运行: pip install urdf-parser-py")
    except Exception as e:
        print(f"解析URDF失败: {e}")

def main():
    urdf_path = "tinystrech/protos/TinyStretch/urdf/TinyStretch_arm.urdf"
    
    if not os.path.exists(urdf_path):
        print(f"URDF文件不存在: {urdf_path}")
        return
    
    print("URDF可视化工具")
    print("=" * 50)
    
    # 打印URDF信息
    print_urdf_info(urdf_path)
    
    print("\n选择可视化方式:")
    print("1. PyBullet 3D可视化 (推荐)")
    print("2. 生成URDF图形")
    print("3. 仅显示信息")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        visualize_with_pybullet(urdf_path)
    elif choice == "2":
        generate_urdf_graph(urdf_path)
    elif choice == "3":
        print("URDF信息已显示")
    elif choice == "4":
        print("退出")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
