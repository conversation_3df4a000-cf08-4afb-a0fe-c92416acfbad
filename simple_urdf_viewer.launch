<?xml version="1.0"?>
<launch>
  <!-- 设置URDF文件路径 -->
  <arg name="urdf_file" default="$(find TinyStretch)/urdf/TinyStretch_arm.urdf" />
  
  <!-- 加载URDF到参数服务器 -->
  <param name="robot_description" textfile="$(arg urdf_file)" />
  
  <!-- 启动joint_state_publisher，带GUI -->
  <node name="joint_state_publisher_gui" pkg="joint_state_publisher_gui" type="joint_state_publisher_gui" />
  
  <!-- 启动robot_state_publisher -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" />
  
  <!-- 启动RViz -->
  <node name="rviz" pkg="rviz" type="rviz" args="-d $(find urdf_tutorial)/rviz/urdf.rviz" required="true" />
</launch>
